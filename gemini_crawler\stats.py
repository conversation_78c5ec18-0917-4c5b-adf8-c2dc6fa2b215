"""
爬虫统计模块

提供爬虫运行统计和监控功能
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import logging


@dataclass
class CrawlerStats:
    """爬虫统计信息"""
    
    # 基础统计
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    
    # 请求统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    waf_blocked_requests: int = 0
    network_error_requests: int = 0
    
    # 数据统计
    total_keys_found: int = 0
    valid_keys: int = 0
    invalid_keys: int = 0
    status_200_keys: int = 0
    duplicate_keys: int = 0
    
    # 错误统计
    parse_errors: int = 0
    validation_errors: int = 0
    other_errors: int = 0
    
    # 详细记录
    error_details: List[Dict[str, Any]] = field(default_factory=list)
    key_types: Dict[str, int] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        self.logger = logging.getLogger(__name__)
    
    def record_request_success(self):
        """记录成功请求"""
        self.total_requests += 1
        self.successful_requests += 1
    
    def record_request_failure(self, error_type: str, error_msg: str = ""):
        """记录失败请求"""
        self.total_requests += 1
        self.failed_requests += 1
        
        if error_type == 'waf_blocked':
            self.waf_blocked_requests += 1
        elif error_type == 'network_error':
            self.network_error_requests += 1
        else:
            self.other_errors += 1
        
        # 记录错误详情
        self.error_details.append({
            'timestamp': datetime.now().isoformat(),
            'type': error_type,
            'message': error_msg
        })
    
    def record_keys_found(self, keys: List[Dict[str, Any]]):
        """记录找到的Key"""
        self.total_keys_found += len(keys)
        
        for key_data in keys:
            # 统计有效性
            if key_data.get('valid', True):
                self.valid_keys += 1
            else:
                self.invalid_keys += 1
            
            # 统计状态
            if key_data.get('status') == 200:
                self.status_200_keys += 1
            
            # 统计类型
            key_type = key_data.get('type', 'unknown')
            self.key_types[key_type] = self.key_types.get(key_type, 0) + 1
    
    def record_parse_error(self, error_msg: str = ""):
        """记录解析错误"""
        self.parse_errors += 1
        self.error_details.append({
            'timestamp': datetime.now().isoformat(),
            'type': 'parse_error',
            'message': error_msg
        })
    
    def record_validation_error(self, error_msg: str = ""):
        """记录验证错误"""
        self.validation_errors += 1
        self.error_details.append({
            'timestamp': datetime.now().isoformat(),
            'type': 'validation_error',
            'message': error_msg
        })
    
    def record_duplicate_keys(self, count: int):
        """记录重复Key数量"""
        self.duplicate_keys += count
    
    def finish(self):
        """完成统计"""
        self.end_time = time.time()
    
    @property
    def duration(self) -> float:
        """获取运行时长（秒）"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def keys_per_second(self) -> float:
        """获取每秒处理Key数"""
        duration = self.duration
        if duration == 0:
            return 0.0
        return self.total_keys_found / duration
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            'duration': self.duration,
            'requests': {
                'total': self.total_requests,
                'successful': self.successful_requests,
                'failed': self.failed_requests,
                'success_rate': f"{self.success_rate:.1f}%"
            },
            'keys': {
                'total_found': self.total_keys_found,
                'valid': self.valid_keys,
                'invalid': self.invalid_keys,
                'status_200': self.status_200_keys,
                'duplicates': self.duplicate_keys,
                'types': dict(self.key_types)
            },
            'errors': {
                'waf_blocked': self.waf_blocked_requests,
                'network_errors': self.network_error_requests,
                'parse_errors': self.parse_errors,
                'validation_errors': self.validation_errors,
                'other_errors': self.other_errors
            },
            'performance': {
                'keys_per_second': f"{self.keys_per_second:.2f}",
                'avg_request_time': f"{self.duration / max(self.total_requests, 1):.2f}s"
            }
        }
    
    def print_summary(self):
        """打印统计摘要"""
        summary = self.get_summary()
        
        print("\n" + "=" * 60)
        print("🎯 爬虫运行统计报告")
        print("=" * 60)
        
        # 基础信息
        print(f"⏱️  运行时长: {summary['duration']:.1f}秒")
        print(f"📊 处理性能: {summary['performance']['keys_per_second']} Keys/秒")
        
        # 请求统计
        req = summary['requests']
        print(f"\n📡 请求统计:")
        print(f"   总请求数: {req['total']}")
        print(f"   成功请求: {req['successful']}")
        print(f"   失败请求: {req['failed']}")
        print(f"   成功率: {req['success_rate']}")
        
        # Key统计
        keys = summary['keys']
        print(f"\n🔑 Key统计:")
        print(f"   发现总数: {keys['total_found']}")
        print(f"   有效Key: {keys['valid']}")
        print(f"   无效Key: {keys['invalid']}")
        print(f"   状态200: {keys['status_200']}")
        print(f"   重复Key: {keys['duplicates']}")
        
        # Key类型分布
        if keys['types']:
            print(f"   类型分布:")
            for key_type, count in keys['types'].items():
                print(f"     {key_type}: {count}")
        
        # 错误统计
        errors = summary['errors']
        total_errors = sum(errors.values())
        if total_errors > 0:
            print(f"\n❌ 错误统计:")
            print(f"   WAF拦截: {errors['waf_blocked']}")
            print(f"   网络错误: {errors['network_errors']}")
            print(f"   解析错误: {errors['parse_errors']}")
            print(f"   验证错误: {errors['validation_errors']}")
            print(f"   其他错误: {errors['other_errors']}")
        
        print("=" * 60)
    
    def get_recent_errors(self, minutes: int = 5) -> List[Dict[str, Any]]:
        """获取最近的错误记录"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_errors = []
        
        for error in self.error_details:
            error_time = datetime.fromisoformat(error['timestamp'])
            if error_time >= cutoff_time:
                recent_errors.append(error)
        
        return recent_errors
    
    def reset(self):
        """重置统计信息"""
        self.__init__()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 100):
        """
        初始化性能监控器
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.request_times = []
        self.parse_times = []
        self.logger = logging.getLogger(__name__)
    
    def record_request_time(self, duration: float):
        """记录请求时间"""
        self.request_times.append(duration)
        if len(self.request_times) > self.window_size:
            self.request_times.pop(0)
    
    def record_parse_time(self, duration: float):
        """记录解析时间"""
        self.parse_times.append(duration)
        if len(self.parse_times) > self.window_size:
            self.parse_times.pop(0)
    
    def get_avg_request_time(self) -> float:
        """获取平均请求时间"""
        if not self.request_times:
            return 0.0
        return sum(self.request_times) / len(self.request_times)
    
    def get_avg_parse_time(self) -> float:
        """获取平均解析时间"""
        if not self.parse_times:
            return 0.0
        return sum(self.parse_times) / len(self.parse_times)
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return {
            'avg_request_time': self.get_avg_request_time(),
            'avg_parse_time': self.get_avg_parse_time(),
            'total_samples': len(self.request_times) + len(self.parse_times)
        }