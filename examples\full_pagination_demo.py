#!/usr/bin/env python3
"""
完整分页爬取演示

演示如何获取网站所有页面的Gemini Key
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from crawl_all_pages import PaginationCrawler
from gemini_crawler import setup_logging, Config


def demo_full_pagination():
    """演示完整分页爬取"""
    print("🚀 完整分页爬取演示")
    print("=" * 50)
    print("⚠️  注意：这将爬取所有47页，大约需要5-10分钟")
    print("💡 建议：首次使用可以先用 --max-pages 10 测试")
    
    # 询问用户确认
    confirm = input("\n是否继续完整爬取？[y/N]: ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建配置（增加延迟避免被封）
    config = Config()
    config.min_delay = 2.0  # 最小延迟2秒
    config.max_delay = 5.0  # 最大延迟5秒
    config.max_retries = 5  # 增加重试次数
    
    # 创建分页爬虫
    crawler = PaginationCrawler(config)
    
    print(f"\n⚙️  爬取配置:")
    print(f"   延迟范围: {config.min_delay}-{config.max_delay}秒")
    print(f"   最大重试: {config.max_retries}次")
    print(f"   超时时间: {config.request_timeout}秒")
    
    start_time = time.time()
    
    try:
        # 执行完整分页爬取
        print(f"\n🎯 开始完整分页爬取...")
        result = crawler.crawl_all_pages()
        
        # 计算耗时
        elapsed_time = time.time() - start_time
        
        # 打印详细摘要
        crawler.print_summary(result)
        
        # 重试失败的页面
        if result['failed_pages']:
            print(f"\n🔄 发现 {len(result['failed_pages'])} 个失败页面，尝试重试...")
            retry_keys = crawler.retry_failed_pages(result['failed_pages'])
            if retry_keys:
                result['keys'].extend(retry_keys)
                result['keys'] = crawler.crawler.parser.deduplicate_keys(result['keys'])
                print(f"✅ 重试获得 {len(retry_keys)} 个额外Key")
        
        # 保存结果
        if result['keys']:
            # 保存多种格式
            json_file = crawler.storage.save_keys(
                result['keys'], 
                format='json',
                filename='complete_all_pages_keys.json'
            )
            
            csv_file = crawler.storage.save_keys(
                result['keys'], 
                format='csv',
                filename='complete_all_pages_keys.csv'
            )
            
            txt_file = crawler.storage.save_keys(
                result['keys'], 
                format='txt',
                filename='complete_all_pages_keys.txt'
            )
            
            # 保存详细统计报告
            report_file = crawler.storage.save_report(
                result['statistics'],
                format='json',
                filename='complete_pagination_report.json'
            )
            
            print(f"\n💾 完整结果已保存:")
            print(f"   JSON文件: {json_file}")
            print(f"   CSV文件: {csv_file}")
            print(f"   TXT文件: {txt_file}")
            print(f"   统计报告: {report_file}")
            
            # 最终统计
            print(f"\n🎉 完整分页爬取成功！")
            print(f"⏱️  总耗时: {elapsed_time/60:.1f}分钟")
            print(f"📊 获取Key: {len(result['keys'])} 个")
            print(f"📄 成功页面: {result['statistics']['successful_pages']}/{result['statistics']['total_pages_attempted']}")
            print(f"⚡ 平均速度: {len(result['keys'])/(elapsed_time/60):.1f} Keys/分钟")
            
            # 显示Key类型分布
            type_distribution = {}
            for key_data in result['keys']:
                key_type = key_data.get('type', 'unknown')
                type_distribution[key_type] = type_distribution.get(key_type, 0) + 1
            
            print(f"\n📈 Key类型分布:")
            for key_type, count in type_distribution.items():
                percentage = (count / len(result['keys'])) * 100
                print(f"   {key_type}: {count} 个 ({percentage:.1f}%)")
            
            # 显示前20个Key
            print(f"\n📋 前20个Key预览:")
            for i, key_data in enumerate(result['keys'][:20], 1):
                print(f"   {i:2d}. {key_data['key']} ({key_data['type']})")
            
            if len(result['keys']) > 20:
                print(f"   ... 还有 {len(result['keys']) - 20} 个Key")
        
        else:
            print("⚠️  未获取到任何Key")
    
    except KeyboardInterrupt:
        elapsed_time = time.time() - start_time
        print(f"\n❌ 用户中断操作（已运行 {elapsed_time/60:.1f}分钟）")
        
        # 即使中断也尝试保存已获取的数据
        if hasattr(crawler, 'total_keys') and crawler.total_keys > 0:
            print("💾 尝试保存已获取的数据...")
            # 这里可以添加保存逻辑
    
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ 爬取失败（已运行 {elapsed_time/60:.1f}分钟）: {e}")
        import traceback
        traceback.print_exc()


def demo_smart_pagination():
    """演示智能分页爬取（推荐）"""
    print("\n🧠 智能分页爬取演示")
    print("=" * 50)
    print("💡 这个模式会先爬取前几页评估网站状态，然后决定策略")
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建分页爬虫
    crawler = PaginationCrawler()
    
    try:
        # 先爬取前3页测试
        print("🧪 测试爬取前3页...")
        test_result = crawler.crawl_all_pages(max_pages=3)
        
        if test_result['failed_pages']:
            print(f"⚠️  测试发现问题，失败页面: {test_result['failed_pages']}")
            print("💡 建议：检查网络连接或Cookie设置")
            return
        
        # 计算平均每页Key数量
        avg_keys_per_page = len(test_result['keys']) / 3
        estimated_total = avg_keys_per_page * 47  # 假设47页
        
        print(f"📊 测试结果:")
        print(f"   前3页获取: {len(test_result['keys'])} 个Key")
        print(f"   平均每页: {avg_keys_per_page:.1f} 个Key")
        print(f"   预估总数: {estimated_total:.0f} 个Key")
        
        # 询问是否继续
        continue_crawl = input(f"\n是否继续爬取剩余页面？[Y/n]: ").strip().lower()
        if continue_crawl in ['n', 'no']:
            print("✅ 测试完成，已保存前3页数据")
            return
        
        # 继续爬取剩余页面
        print("\n🚀 继续爬取剩余页面...")
        remaining_result = crawler.crawl_all_pages(start_page=4)
        
        # 合并结果
        all_keys = test_result['keys'] + remaining_result['keys']
        all_keys = crawler.crawler.parser.deduplicate_keys(all_keys)
        
        print(f"\n🎉 智能分页爬取完成！")
        print(f"📊 总共获取: {len(all_keys)} 个唯一Key")
        
        # 保存最终结果
        final_file = crawler.storage.save_keys(
            all_keys,
            format='json',
            filename='smart_pagination_keys.json'
        )
        print(f"💾 结果已保存: {final_file}")
        
    except Exception as e:
        print(f"❌ 智能爬取失败: {e}")


def main():
    """主函数"""
    print("🔍 Gemini Key Crawler - 分页爬取演示")
    print("=" * 60)
    
    print("选择演示模式:")
    print("1. 完整分页爬取（所有47页，耗时较长）")
    print("2. 智能分页爬取（先测试再决定，推荐）")
    print("3. 退出")
    
    choice = input("\n请选择 [1-3]: ").strip()
    
    if choice == '1':
        demo_full_pagination()
    elif choice == '2':
        demo_smart_pagination()
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()