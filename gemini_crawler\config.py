"""
配置管理模块

提供爬虫的各种配置参数，包括User-Agent池、<PERSON><PERSON>管理、基础设置等
"""

import os
import random
from typing import Dict, List, Optional


class Config:
    """爬虫配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        # 基础URL配置
        self.base_url = "https://geminikeyseeker.o0o.moe"
        self.target_url = f"{self.base_url}/?status=200"
        
        # 请求延迟配置（秒）
        self.min_delay = float(os.getenv('CRAWLER_MIN_DELAY', '1.0'))
        self.max_delay = float(os.getenv('CRAWLER_MAX_DELAY', '5.0'))
        
        # 重试配置
        self.max_retries = int(os.getenv('CRAWLER_MAX_RETRIES', '3'))
        self.retry_delay = float(os.getenv('CRAWLER_RETRY_DELAY', '2.0'))
        
        # 超时配置
        self.request_timeout = float(os.getenv('CRAWLER_TIMEOUT', '30.0'))
        
        # User-Agent池
        self.user_agents = [
            # Chrome Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            
            # Firefox Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0",
            
            # Edge Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
            
            # Chrome macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            
            # Safari macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
            
            # Chrome Linux
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        ]
        
        # 默认Cookie字符串（用户提供的）
        self.default_cookie_string = (
            "x_clck=7wbgdh%7C2%7Cfy2%7C0%7C2038; "
            "_clsk=1p8olje%7C1753936853195%7C3%7C1%7Cs.clarity.ms%2Fcollect; "
            "CLID=87dacc7ac93b4b4791317504995c9383.20250731.20260731; "
            "MUID=10B84E2E087C6FA830E0581409FF6E84; "
            "sl_jwt_session=N7qLNI69rQgmmjr8aQ8XP7wIEHLb5zF3yVQGkUp4xD0z/NbEMSWO2DgWwQFglH64; "
            "sl_jwt_sign=; "
            "sl-challenge-server=cloud; "
            "sl-session=bs5OejlFjGhU6BBYw0/sqQ==; "
            "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTI0MDYzLCJ1c2VybmFtZSI6InNtazk2IiwibmFtZSI6IlNtazk2IiwidHJ1c3RfbGV2ZWwiOjN9.BZlLyXnYWTpZ0uBtYbOSoMnpbBZLdmFnOeneGut0lmA"
        )
        
        # 解析Cookie
        self.cookies = self.parse_cookie_string(
            os.getenv('GEMINI_COOKIES', self.default_cookie_string)
        )
        
        # 基础请求头模板
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
    
    def parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析Cookie字符串为字典格式
        
        Args:
            cookie_string: Cookie字符串，格式如 "key1=value1; key2=value2"
            
        Returns:
            Dict[str, str]: Cookie字典
        """
        if not cookie_string:
            return {}
        
        cookies = {}
        try:
            # 分割Cookie字符串
            cookie_pairs = cookie_string.split(';')
            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    cookies[key.strip()] = value.strip()
        except Exception as e:
            print(f"警告：Cookie解析失败: {e}")
            return {}
        
        return cookies
    
    def get_random_user_agent(self) -> str:
        """
        随机获取一个User-Agent
        
        Returns:
            str: 随机选择的User-Agent字符串
        """
        return random.choice(self.user_agents)
    
    def get_random_delay(self) -> float:
        """
        获取随机延迟时间
        
        Returns:
            float: 随机延迟时间（秒）
        """
        return random.uniform(self.min_delay, self.max_delay)
    
    def get_headers(self, custom_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        获取完整的请求头
        
        Args:
            custom_headers: 自定义请求头，会覆盖默认值
            
        Returns:
            Dict[str, str]: 完整的请求头字典
        """
        headers = self.base_headers.copy()
        headers['User-Agent'] = self.get_random_user_agent()
        
        if custom_headers:
            headers.update(custom_headers)
        
        return headers
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        # 检查基本配置
        if not self.base_url or not self.target_url:
            print("错误：URL配置无效")
            return False
        
        # 检查User-Agent池
        if not self.user_agents or len(self.user_agents) < 5:
            print("错误：User-Agent池配置不足")
            return False
        
        # 检查Cookie
        if not self.cookies:
            print("警告：未配置Cookie，可能影响访问")
        
        # 检查延迟配置
        if self.min_delay < 0 or self.max_delay < self.min_delay:
            print("错误：延迟配置无效")
            return False
        
        return True
    
    def __str__(self) -> str:
        """返回配置信息的字符串表示"""
        return f"""
Gemini Crawler 配置信息:
- 目标URL: {self.target_url}
- User-Agent池大小: {len(self.user_agents)}
- Cookie数量: {len(self.cookies)}
- 延迟范围: {self.min_delay}-{self.max_delay}秒
- 最大重试次数: {self.max_retries}
- 请求超时: {self.request_timeout}秒
        """.strip()


# 创建默认配置实例
default_config = Config()