#!/usr/bin/env python3
"""
测试分页URL
"""

import sys
import os
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import <PERSON><PERSON>eyCrawler, Config, setup_logging


def test_pagination_urls():
    """测试分页URL"""
    print("🧪 测试分页URL...")
    
    setup_logging('INFO')
    
    # 创建爬虫
    crawler = GeminiKeyCrawler()
    
    # 测试的页面
    test_pages = [1, 2, 3, 47]  # 测试第1页、第2页、第3页和最后一页
    
    try:
        with crawler._http_client_context() as client:
            for page_num in test_pages:
                print(f"\n📄 测试第 {page_num} 页...")
                
                # 构建URL
                url = f"https://geminikeyseeker.o0o.moe/?page={page_num}&status=200&sort_by=last_checked_time"
                print(f"🔗 URL: {url}")
                
                try:
                    response = client.get(url)
                    print(f"✅ 状态码: {response.status_code}")
                    print(f"📏 内容长度: {len(response.text)} 字符")
                    
                    # 统计Key数量
                    key_pattern = r'AIza[0-9A-Za-z-_]{35}'
                    keys = re.findall(key_pattern, response.text)
                    print(f"🔑 找到Key数量: {len(keys)}")
                    
                    # 显示前3个Key
                    if keys:
                        print("📋 前3个Key:")
                        for i, key in enumerate(keys[:3], 1):
                            print(f"   {i}. {key}")
                    
                    # 检查是否有分页导航
                    if 'pagination' in response.text:
                        print("✅ 包含分页导航")
                    
                    # 检查页码指示
                    page_indicators = re.findall(rf'page.*{page_num}', response.text, re.IGNORECASE)
                    if page_indicators:
                        print(f"✅ 找到页码指示: {page_indicators[:2]}")
                    
                except Exception as e:
                    print(f"❌ 请求失败: {e}")
                
                print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    test_pagination_urls()