# Gemini Key Crawler

一个轻量级的Gemini API Key爬虫工具，专门用于获取 `geminikeyseeker.o0o.moe` 网站上状态为200的有效Gemini Key。

## ✨ 特性

- 🛡️ **反检测机制**: 专门针对雷池WAF进行优化，包含完整的反检测策略
- 🔄 **智能重试**: 多层重试机制，自动处理网络错误和WAF拦截
- 📊 **多格式输出**: 支持JSON、CSV、TXT、Excel四种输出格式
- 🔍 **数据验证**: 支持多种Gemini Key格式验证，自动过滤无效Key
- 📈 **详细统计**: 完整的爬取统计和性能监控
- 🎛️ **灵活配置**: 命令行参数、配置文件、交互式配置多种方式
- 📁 **文件管理**: 自动文件命名、备份、清理等管理功能

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基础使用

```bash
# 使用默认配置运行（默认keys-only格式，每行一个Key）
python main.py

# 指定输出格式和文件
python main.py --format json --output my_keys.json
python main.py --format keys-only --output my_keys.txt

# 使用自定义Cookie
python main.py --cookies "your_cookie_string_here"

# 交互式配置
python main.py --interactive
```

## 📖 详细使用说明

### 命令行参数

#### 基础选项

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| `--url` | `-u` | 目标URL | `https://geminikeyseeker.o0o.moe/?status=200` |
| `--output` | `-o` | 输出文件路径 | 自动生成 |
| `--format` | `-f` | 输出格式 (keys-only/json/csv/txt/xlsx) | `keys-only` |
| `--output-dir` | `-d` | 输出目录 | `results` |

#### 爬虫配置

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| `--cookies` | `-c` | Cookie字符串 | 使用内置Cookie |
| `--max-retries` | `-r` | 最大重试次数 | `3` |
| `--delay` | | 请求延迟时间(秒) | `1.0-5.0随机` |
| `--timeout` | | 请求超时时间(秒) | `30.0` |

#### 功能选项

| 参数 | 简写 | 说明 |
|------|------|------|
| `--interactive` | `-i` | 交互式配置向导 |
| `--test-connection` | `-t` | 测试连接并退出 |
| `--list-files` | `-l` | 列出输出目录中的文件 |
| `--clean-old-files DAYS` | | 清理N天前的旧文件 |
| `--all-pages` | | 爬取所有页面（自动检测总页数） |
| `--max-pages` | | 最大爬取页数（配合--all-pages使用） |
| `--start-page` | | 起始页码（默认: 1） |

#### 输出控制

| 参数 | 简写 | 说明 |
|------|------|------|
| `--verbose` | `-v` | 详细输出 |
| `--quiet` | `-q` | 静默模式，只输出错误 |
| `--no-progress` | | 不显示进度条 |
| `--no-stats` | | 不显示统计信息 |

#### 高级选项

| 参数 | 说明 |
|------|------|
| `--config-file` | 配置文件路径 (JSON格式) |
| `--save-config` | 保存当前配置到文件 |
| `--version` | 显示版本信息 |

### 使用示例

#### 1. 基础爬取

```bash
# 最简单的使用方式（默认keys-only格式）
python main.py

# 指定输出格式
python main.py --format json
python main.py --format csv
python main.py --format keys-only

# 指定输出文件
python main.py --output my_keys.txt

# 爬取所有页面
python main.py --all-pages

# 爬取前10页
python main.py --all-pages --max-pages 10
```

#### 2. 使用Cookie认证

```bash
# 使用自定义Cookie
python main.py --cookies "x_clck=7wbgdh%7C2%7Cfy2%7C0%7C2038; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 从文件读取Cookie (需要先创建配置文件)
python main.py --config-file config.json
```

#### 3. 交互式配置

```bash
python main.py --interactive
```

交互式配置会引导您设置：
- 目标URL
- 输出格式和目录
- Cookie字符串
- 高级参数（重试次数、延迟时间等）

#### 4. 测试和调试

```bash
# 测试连接
python main.py --test-connection

# 详细输出模式
python main.py --verbose

# 静默模式
python main.py --quiet
```

#### 5. 文件管理

```bash
# 列出输出文件
python main.py --list-files

# 清理7天前的旧文件
python main.py --clean-old-files 7
```

#### 6. 配置文件使用

创建配置文件 `config.json`：

```json
{
  "url": "https://geminikeyseeker.o0o.moe/?status=200",
  "format": "json",
  "output_dir": "results",
  "cookies": "your_cookie_string_here",
  "max_retries": 5,
  "delay": 2.0,
  "timeout": 60.0
}
```

使用配置文件：

```bash
python main.py --config-file config.json
```

保存当前配置：

```bash
python main.py --save-config my_config.json --format csv --max-retries 5
```

#### 7. 分页爬取（获取全部Key）

网站采用分页显示，默认只爬取第一页。要获取所有页面的Key：

```bash
# 自动检测并爬取所有页面（推荐）
python main.py --all-pages

# 限制最大页数（避免爬取时间过长）
python main.py --all-pages --max-pages 20

# 从指定页开始爬取
python main.py --all-pages --start-page 5 --max-pages 10

# 分页爬取并保存为CSV格式
python main.py --all-pages --format csv --output all_keys.csv

# 静默模式分页爬取（适合后台运行）
python main.py --all-pages --quiet
```

**分页爬取特点**：
- 🔍 **自动检测总页数**：无需手动指定，自动分析网站分页结构
- 📊 **实时进度显示**：显示当前页数、进度百分比和预计剩余时间
- 🔄 **智能重试机制**：失败页面自动重试，确保数据完整性
- ⏱️ **防封延迟**：页面间自动延迟，避免触发反爬虫机制
- 📈 **详细统计**：每页统计和全局统计，便于分析爬取效果
- 🔗 **全局去重**：跨页面去重，确保Key的唯一性

## 📁 输出格式说明

### Keys-Only格式 (默认推荐)

纯Key列表，每行一个Key，无其他信息：

```
AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK
AIzaSyDZSf7H6TrRkp80Y6u6OZD-iJcYRS3S_4s
AIzaSyD3TviG96fpnjpa8nk8ym6eYvnYu20qVoQ
sk-1234567890abcdef1234567890abcdef1234567890abcdef
gsk_1234567890abcdef1234567890abcdef
```

**优点**：
- 🎯 **简洁明了**：只包含Key，无多余信息
- 📋 **易于使用**：可直接复制粘贴使用
- 💾 **文件小**：占用空间最少
- 🔧 **兼容性好**：任何文本编辑器都能打开

### JSON格式

```json
{
  "keys": [
    {
      "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
      "status": 200,
      "type": "google_ai_studio",
      "source": "api",
      "validated": true,
      "validation_time": 1640995200.0
    }
  ],
  "metadata": {
    "timestamp": "2024-01-01T12:00:00",
    "total_count": 1,
    "format": "json",
    "version": "1.0",
    "generated_by": "Gemini Key Crawler"
  }
}
```

### CSV格式

```csv
# Generated by Gemini Key Crawler
# Timestamp: 2024-01-01T12:00:00
# Total Count: 1

key,status,type,source,validated
AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK,200,google_ai_studio,api,true
```

### TXT格式

```
# Gemini Key Crawler Results
# Generated: 2024-01-01T12:00:00
# Total Keys: 1
# Format: Plain Text

   1. AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK
      Status: 200, Type: google_ai_studio

```

### Excel格式

包含两个工作表：
- **Keys**: 主要数据
- **Metadata**: 元数据信息

## 🔧 高级配置

### Cookie配置

Cookie是访问网站的关键认证信息。您可以通过以下方式获取Cookie：

1. 打开浏览器开发者工具 (F12)
2. 访问目标网站
3. 在Network标签页中找到请求
4. 复制Cookie字符串

Cookie格式示例：
```
x_clck=7wbgdh%7C2%7Cfy2%7C0%7C2038; _clsk=1p8olje%7C1753936853195%7C3%7C1%7Cs.clarity.ms%2Fcollect; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 反检测配置

爬虫内置了多种反检测机制：

- **随机User-Agent**: 14个真实浏览器UA随机选择
- **请求头伪装**: 完整的Chrome浏览器特征头
- **智能延迟**: 1-5秒随机延迟，模拟人类行为
- **重试策略**: 指数退避算法，WAF拦截时增加延迟

### 性能调优

根据网站响应情况调整参数：

```bash
# 保守模式 (推荐)
python main.py --delay 3.0 --max-retries 5 --timeout 60

# 快速模式 (可能被拦截)
python main.py --delay 1.0 --max-retries 2 --timeout 15

# 调试模式
python main.py --verbose --test-connection
```

## 📊 统计信息说明

爬虫会生成详细的统计报告，包括：

### 请求统计
- 总请求数
- 成功/失败请求数
- 成功率

### Key统计
- 发现的Key总数
- 有效/无效Key数量
- 状态为200的Key数量
- 重复Key数量
- Key类型分布

### 错误统计
- WAF拦截次数
- 网络错误次数
- 解析错误次数
- 验证错误次数

### 性能统计
- 运行时长
- 处理速度 (Keys/秒)
- 平均响应时间

## 🛠️ 故障排除

### 常见问题

#### 1. WAF拦截（最常见问题）

**现象**: 出现"WAF拦截"或"检测到WAF拦截: 468"错误

**主要原因**: 频繁请求导致IP被临时限制

**解决方案**:
1. **等待恢复**（推荐）: 等待1-2小时后重试
2. **更换网络**: 使用不同网络连接（如手机热点）
3. **检查Cookie**: 确认Cookie是否过期
4. **降低频率**: 使用更保守的延迟设置

**详细排查**: 查看 `WAF_TROUBLESHOOTING.md` 文件获取完整的排查指南

#### 2. 网络连接问题

**现象**: 连接超时或网络错误

**解决方案**:
- 检查网络连接
- 增加超时时间: `--timeout 60`
- 使用测试连接: `--test-connection`

#### 3. 没有获取到Key

**现象**: 爬取完成但Key数量为0

**解决方案**:
- 检查目标网站是否有数据
- 验证Cookie是否有效
- 使用详细模式查看日志: `--verbose`

#### 4. 文件保存失败

**现象**: 无法保存输出文件

**解决方案**:
- 检查输出目录权限
- 确保磁盘空间充足
- 检查文件名是否合法

### 调试技巧

```bash
# 详细日志输出
python main.py --verbose

# 测试连接
python main.py --test-connection --verbose

# 检查配置
python main.py --interactive

# 查看帮助
python main.py --help
```

## 📝 开发说明

### 项目结构

```
gemini_crawler/
├── __init__.py          # 包初始化
├── config.py            # 配置管理
├── http_client.py       # HTTP客户端
├── parsers.py           # 数据解析器
├── validators.py        # 数据验证器
├── crawler.py           # 主爬虫类
├── storage.py           # 数据存储
├── stats.py             # 统计监控
├── utils.py             # 工具函数
└── exceptions.py        # 异常定义

main.py                  # 命令行入口
requirements.txt         # 依赖包
README.md               # 使用文档
examples/               # 使用示例
results/                # 输出目录
```

### 扩展开发

如果需要扩展功能，可以：

1. **添加新的Key格式**: 修改 `validators.py` 中的验证规则
2. **支持新的输出格式**: 在 `storage.py` 中添加新的保存方法
3. **增强反检测**: 在 `http_client.py` 中添加新的反检测策略
4. **自定义解析器**: 在 `parsers.py` 中添加新的数据解析方法

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## ⚠️ 免责声明

- 本工具仅供学习和研究使用
- 使用者需要遵守目标网站的robots.txt和使用条款
- 请合理控制爬取频率，避免对目标网站造成压力
- 作者不对使用本工具产生的任何后果负责

## 📞 支持

如果您在使用过程中遇到问题，可以：

1. 查看本文档的故障排除部分
2. 使用 `--verbose` 模式获取详细日志
3. 提交Issue描述问题和错误信息

## 🎯 快速上手指南

### 第一次使用

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **基础爬取**（推荐新手）：
   ```bash
   # 爬取第一页，输出纯Key列表
   python main.py
   ```

3. **获取更多Key**：
   ```bash
   # 爬取前10页
   python main.py --all-pages --max-pages 10

   # 爬取所有页面（约47页，需要8-12分钟）
   python main.py --all-pages
   ```

4. **查看结果**：
   ```bash
   # 列出生成的文件
   python main.py --list-files
   ```

### 输出格式对比

| 格式 | 文件大小 | 优点 | 适用场景 |
|------|----------|------|----------|
| **keys-only** | 最小 | 纯Key列表，直接可用 | 日常使用，复制粘贴 |
| **json** | 较大 | 结构化，包含元数据 | 程序处理，数据分析 |
| **csv** | 中等 | 表格格式，Excel兼容 | 数据分析，统计报表 |
| **txt** | 中等 | 可读性好，包含状态 | 人工查看，文档记录 |

### 性能优化建议

- 🚀 **首次使用**：建议先用 `--max-pages 5` 测试
- ⏱️ **时间充足**：使用 `--all-pages` 获取完整数据
- 🛡️ **遇到拦截**：等待几分钟后重试，或检查Cookie
- 💾 **节省空间**：使用 `--format keys-only`（默认）

---

## 🙏 感谢使用

感谢您使用 **Gemini Key Crawler**！这个工具专为获取有效的Gemini API Key而设计。

### 🌟 主要特色

- ✅ **高成功率**：专业的反WAF机制，稳定绕过雷池保护
- 🚀 **高效率**：支持分页爬取，可获取所有47页数据
- 📁 **多格式**：支持5种输出格式，满足不同需求
- 🛡️ **安全可靠**：智能延迟，避免IP封禁
- 📊 **详细统计**：完整的爬取报告和性能监控

### 💡 使用技巧

1. **默认就是最佳**：程序默认使用 `keys-only` 格式，每行一个Key，最适合日常使用
2. **分批爬取**：建议先爬取少量页面测试，确认无问题后再全量爬取
3. **定期更新**：Key状态会变化，建议定期重新爬取获取最新数据
4. **备份重要数据**：程序会自动生成时间戳文件名，避免覆盖

### 📞 技术支持

- 📖 **详细文档**：本README包含完整的使用说明
- 🔧 **故障排除**：查看"故障排除"章节解决常见问题
- 💬 **问题反馈**：遇到问题请提供详细的错误信息

### ⚠️ 使用提醒

- 🎯 **合理使用**：请遵守网站使用条款，合理控制爬取频率
- 🔒 **数据安全**：获取的Key请妥善保管，避免泄露
- 📋 **及时验证**：建议使用前验证Key的有效性

---

**Happy Crawling! 🚀**

*让获取Gemini Key变得简单高效！*