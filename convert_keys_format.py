#!/usr/bin/env python3
"""
转换Key格式 - 将JSON格式转换为每行一个Key的TXT格式
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import DataStorage


def convert_json_to_simple_txt(json_file, output_file):
    """将JSON格式的Key文件转换为简单的TXT格式"""
    print(f"🔄 转换文件: {json_file}")
    
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        keys = data.get('keys', [])
        print(f"📊 找到 {len(keys)} 个Key")
        
        # 写入TXT文件，每行一个Key
        with open(output_file, 'w', encoding='utf-8') as f:
            for key_data in keys:
                key = key_data.get('key', '')
                if key:
                    f.write(key + '\n')
        
        print(f"✅ 转换完成: {output_file}")
        print(f"📄 格式: 每行一个Key，共 {len(keys)} 行")
        
        # 显示前10个Key预览
        print(f"\n📋 前10个Key预览:")
        for i, key_data in enumerate(keys[:10], 1):
            print(f"   {i:2d}. {key_data.get('key', '')}")
        
        if len(keys) > 10:
            print(f"   ... 还有 {len(keys) - 10} 个Key")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False


def main():
    """主函数"""
    print("🔄 Key格式转换工具")
    print("=" * 40)
    
    # 找到最新的JSON文件
    storage = DataStorage()
    files = storage.list_output_files("*.json")
    
    if not files:
        print("❌ 未找到JSON文件")
        return
    
    # 选择最新的文件
    latest_file = files[0]['path']  # 文件已按修改时间排序
    print(f"📁 最新文件: {os.path.basename(latest_file)}")
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(latest_file))[0]
    output_file = f"results/{base_name}_keys_only.txt"
    
    # 执行转换
    success = convert_json_to_simple_txt(latest_file, output_file)
    
    if success:
        print(f"\n🎉 转换成功！")
        print(f"💾 输出文件: {output_file}")
        print(f"📝 格式: 每行一个Key，无其他信息")
    else:
        print(f"\n❌ 转换失败")


if __name__ == "__main__":
    main()