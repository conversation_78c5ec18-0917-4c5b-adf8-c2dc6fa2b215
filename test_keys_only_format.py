#!/usr/bin/env python3
"""
测试keys-only格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import DataStorage


def test_keys_only_format():
    """测试keys-only格式"""
    print("🧪 测试keys-only格式")
    print("=" * 30)
    
    # 创建测试数据
    test_keys = [
        {
            "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
            "status": 200,
            "type": "google_ai_studio",
            "source": "api",
            "validated": True
        },
        {
            "key": "AIzaSyDZSf7H6TrRkp80Y6u6OZD-iJcYRS3S_4s",
            "status": 200,
            "type": "google_ai_studio",
            "source": "web",
            "validated": True
        },
        {
            "key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef",
            "status": 200,
            "type": "openai_format",
            "source": "scrape",
            "validated": True
        },
        {
            "key": "gsk_1234567890abcdef1234567890abcdef",
            "status": 403,
            "type": "google_service",
            "source": "manual",
            "validated": False
        }
    ]
    
    # 创建存储实例
    storage = DataStorage()
    
    # 测试keys-only格式
    print("💾 保存为keys-only格式...")
    keys_only_file = storage.save_keys(
        test_keys, 
        format='keys-only',
        filename='test_keys_only.txt'
    )
    
    print(f"✅ 保存成功: {keys_only_file}")
    
    # 读取并显示内容
    print(f"\n📄 文件内容:")
    with open(keys_only_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    # 统计行数
    lines = content.strip().split('\n')
    print(f"📊 统计信息:")
    print(f"   总行数: {len(lines)}")
    print(f"   文件大小: {os.path.getsize(keys_only_file)} 字节")
    
    # 对比其他格式
    print(f"\n🔄 对比其他格式:")
    
    # JSON格式
    json_file = storage.save_keys(test_keys, format='json', filename='test_compare.json')
    json_size = os.path.getsize(json_file)
    print(f"   JSON格式: {json_size} 字节")
    
    # CSV格式
    csv_file = storage.save_keys(test_keys, format='csv', filename='test_compare.csv')
    csv_size = os.path.getsize(csv_file)
    print(f"   CSV格式: {csv_size} 字节")
    
    # TXT格式
    txt_file = storage.save_keys(test_keys, format='txt', filename='test_compare.txt')
    txt_size = os.path.getsize(txt_file)
    print(f"   TXT格式: {txt_size} 字节")
    
    keys_only_size = os.path.getsize(keys_only_file)
    print(f"   Keys-Only格式: {keys_only_size} 字节")
    
    print(f"\n📈 文件大小对比:")
    print(f"   Keys-Only vs JSON: {((json_size - keys_only_size) / json_size * 100):.1f}% 更小")
    print(f"   Keys-Only vs CSV: {((csv_size - keys_only_size) / csv_size * 100):.1f}% 更小")
    print(f"   Keys-Only vs TXT: {((txt_size - keys_only_size) / txt_size * 100):.1f}% 更小")
    
    return True


if __name__ == "__main__":
    test_keys_only_format()