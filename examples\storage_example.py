#!/usr/bin/env python3
"""
数据存储功能使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gemini_crawler import DataStorage, setup_logging


def main():
    """主示例函数"""
    print("📁 Gemini Key Crawler - 数据存储示例")
    print("=" * 50)
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建存储实例
    storage = DataStorage("examples/output")
    
    # 示例数据
    sample_keys = [
        {
            "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
            "status": 200,
            "type": "google_ai_studio",
            "source": "api",
            "validated": True
        },
        {
            "key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef",
            "status": 200,
            "type": "openai_format",
            "source": "web",
            "validated": True
        },
        {
            "key": "gsk_1234567890abcdef1234567890abcdef",
            "status": 403,
            "type": "google_service",
            "source": "scrape",
            "validated": False
        }
    ]
    
    print(f"📊 准备保存 {len(sample_keys)} 个Key...")
    
    # 保存为不同格式
    formats = ['json', 'csv', 'txt']
    saved_files = []
    
    for format in formats:
        try:
            filepath = storage.save_keys(sample_keys, format=format)
            saved_files.append(filepath)
            print(f"✅ {format.upper()}格式保存成功: {os.path.basename(filepath)}")
        except Exception as e:
            print(f"❌ {format.upper()}格式保存失败: {e}")
    
    # 生成统计报告
    sample_stats = {
        'duration': 15.2,
        'requests': {
            'total': 3,
            'successful': 2,
            'failed': 1,
            'success_rate': '66.7%'
        },
        'keys': {
            'total_found': 3,
            'valid': 2,
            'invalid': 1,
            'status_200': 2,
            'duplicates': 0,
            'types': {
                'google_ai_studio': 1,
                'openai_format': 1,
                'google_service': 1
            }
        },
        'errors': {
            'waf_blocked': 1,
            'network_errors': 0,
            'parse_errors': 0,
            'validation_errors': 0
        }
    }
    
    # 生成和保存报告
    report = storage.generate_summary_report(sample_keys, sample_stats)
    report_file = storage.save_report(report, format='json')
    txt_report_file = storage.save_report(sample_stats, format='txt')
    
    print(f"📋 报告生成成功:")
    print(f"   JSON报告: {os.path.basename(report_file)}")
    print(f"   TXT报告: {os.path.basename(txt_report_file)}")
    
    # 显示存储信息
    storage_info = storage.get_storage_info()
    print(f"\n📁 存储信息:")
    print(f"   输出目录: {storage_info['output_directory']}")
    print(f"   文件总数: {storage_info['total_files']}")
    print(f"   总大小: {storage_info['total_size_formatted']}")
    
    # 列出所有文件
    print(f"\n📄 输出文件列表:")
    files = storage.list_output_files()
    for i, file_info in enumerate(files, 1):
        print(f"   {i}. {file_info['name']} ({file_info['size_formatted']})")
    
    print(f"\n🎉 示例完成！请查看 {storage_info['output_directory']} 目录中的文件。")


if __name__ == "__main__":
    main()