#!/usr/bin/env python3
"""
诊断WAF拦截问题
"""

import sys
import os
import time
import requests
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import Config, setup_logging


def test_basic_request():
    """测试基础请求"""
    print("🔍 诊断WAF拦截问题")
    print("=" * 40)
    
    url = "https://geminikeyseeker.o0o.moe/?status=200"
    
    # 测试1：无Cookie的基础请求
    print("🧪 测试1: 无Cookie的基础请求")
    try:
        response = requests.get(url, timeout=30)
        print(f"   状态码: {response.status_code}")
        print(f"   内容长度: {len(response.text)}")
        if response.status_code == 468:
            print("   ❌ WAF拦截（468错误）")
        elif response.status_code == 200:
            print("   ✅ 请求成功")
        else:
            print(f"   ⚠️  其他状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print()
    
    # 测试2：使用配置的Cookie
    print("🧪 测试2: 使用配置的Cookie")
    config = Config()
    headers = {
        'User-Agent': config.get_random_user_agent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        response = requests.get(url, headers=headers, cookies=config.cookies, timeout=30)
        print(f"   状态码: {response.status_code}")
        print(f"   内容长度: {len(response.text)}")
        if response.status_code == 468:
            print("   ❌ WAF拦截（468错误）")
        elif response.status_code == 200:
            print("   ✅ 请求成功")
            # 检查是否包含Key
            if 'AIza' in response.text:
                print("   ✅ 响应包含Key数据")
            else:
                print("   ⚠️  响应不包含Key数据")
        else:
            print(f"   ⚠️  其他状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print()
    
    # 测试3：检查Cookie有效性
    print("🧪 测试3: 检查Cookie组件")
    print(f"   Cookie数量: {len(config.cookies)}")
    for key, value in config.cookies.items():
        print(f"   {key}: {value[:20]}..." if len(value) > 20 else f"   {key}: {value}")
    
    print()
    
    # 测试4：尝试不同的User-Agent
    print("🧪 测试4: 尝试不同的User-Agent")
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    
    for i, ua in enumerate(user_agents, 1):
        print(f"   测试UA {i}: {ua[:50]}...")
        test_headers = headers.copy()
        test_headers['User-Agent'] = ua
        
        try:
            response = requests.get(url, headers=test_headers, cookies=config.cookies, timeout=15)
            print(f"     状态码: {response.status_code}")
            if response.status_code == 200:
                print("     ✅ 成功")
                break
            elif response.status_code == 468:
                print("     ❌ WAF拦截")
            else:
                print(f"     ⚠️  状态码: {response.status_code}")
        except Exception as e:
            print(f"     ❌ 失败: {e}")
        
        # 添加延迟避免频繁请求
        time.sleep(2)
    
    print()
    
    # 测试5：检查IP是否被限制
    print("🧪 测试5: 检查IP限制状态")
    print("   等待30秒后重试...")
    time.sleep(30)
    
    try:
        response = requests.get(url, headers=headers, cookies=config.cookies, timeout=30)
        print(f"   30秒后状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ IP限制已解除")
        elif response.status_code == 468:
            print("   ❌ 仍然被WAF拦截")
        else:
            print(f"   ⚠️  其他状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print("\n" + "=" * 40)
    print("🔧 建议解决方案:")
    print("1. 如果所有测试都是468错误，可能是Cookie过期")
    print("2. 如果等待后恢复，说明是频率限制")
    print("3. 如果某个UA成功，可以更新配置使用该UA")
    print("4. 可以尝试更换网络环境或使用代理")


if __name__ == "__main__":
    setup_logging('INFO')
    test_basic_request()