# WAF拦截问题排查指南

## 🛡️ 问题现象

当您看到以下错误信息时，说明遇到了WAF（Web Application Firewall）拦截：

```
WARNING - 检测到WAF拦截: 468
ERROR - 连接测试失败: 请求被WAF拦截: 468
```

## 🔍 问题原因分析

### 1. 频繁请求导致IP限制
- **最常见原因**：短时间内发送过多请求
- **触发条件**：通常5-10分钟内超过20-30次请求
- **表现**：所有请求都返回468状态码

### 2. Cookie过期或无效
- **原因**：网站Cookie有时效性
- **表现**：之前能正常访问，突然开始被拦截

### 3. WAF规则更新
- **原因**：网站更新了防护规则
- **表现**：之前的访问方式突然失效

## 🔧 解决方案

### 方案1：等待IP限制解除（推荐）

```bash
# 等待1-2小时后重试
python main.py --test-connection
```

**说明**：大多数WAF的IP限制是临时的，通常1-2小时后自动解除。

### 方案2：使用更保守的延迟设置

```bash
# 使用环境变量设置更长延迟
set CRAWLER_MIN_DELAY=5.0
set CRAWLER_MAX_DELAY=10.0
python main.py --test-connection
```

### 方案3：更换网络环境

- 使用不同的网络连接（如手机热点）
- 使用VPN或代理服务
- 更换公网IP

### 方案4：更新Cookie（如果Cookie过期）

1. 打开浏览器访问目标网站
2. 按F12打开开发者工具
3. 在Network标签页中找到请求
4. 复制新的Cookie字符串
5. 更新配置：

```bash
python main.py --cookies "新的Cookie字符串"
```

## 🚀 预防措施

### 1. 合理控制爬取频率

```bash
# 推荐设置：每页间隔5-10秒
python main.py --all-pages --max-pages 10
```

### 2. 分批爬取

```bash
# 先爬取少量页面测试
python main.py --all-pages --max-pages 5

# 确认无问题后继续
python main.py --all-pages --start-page 6 --max-pages 10
```

### 3. 使用测试连接功能

```bash
# 爬取前先测试连接
python main.py --test-connection
```

## 📊 WAF状态检查

使用诊断脚本检查当前状态：

```bash
python diagnose_waf_issue.py
```

该脚本会：
- 测试基础连接
- 检查Cookie有效性
- 尝试不同User-Agent
- 分析拦截原因

## 🕐 恢复时间表

| 拦截类型 | 预计恢复时间 | 建议操作 |
|----------|--------------|----------|
| 频率限制 | 30分钟-2小时 | 等待后重试 |
| IP黑名单 | 2-24小时 | 更换IP或等待 |
| Cookie过期 | 立即 | 更新Cookie |
| 规则更新 | 不确定 | 分析新规则 |

## 💡 最佳实践

### 1. 日常使用建议

```bash
# 推荐的日常使用方式
python main.py --all-pages --max-pages 20 --format keys-only
```

### 2. 大量爬取建议

```bash
# 分批进行，每批间隔1小时
python main.py --all-pages --max-pages 15 --start-page 1
# 等待1小时
python main.py --all-pages --max-pages 15 --start-page 16
# 等待1小时
python main.py --all-pages --max-pages 17 --start-page 31
```

### 3. 监控和日志

```bash
# 使用详细日志监控状态
python main.py --verbose --all-pages --max-pages 10
```

## 🆘 紧急恢复

如果急需使用且被长时间拦截：

1. **更换网络**：使用手机热点或其他网络
2. **使用代理**：配置HTTP代理
3. **等待深夜**：通常深夜访问压力小，限制较松
4. **联系管理员**：如果有网站联系方式

## 📞 技术支持

如果以上方法都无效，可能是：
- 网站永久更改了访问策略
- 需要新的认证方式
- WAF规则发生重大更新

建议：
1. 检查网站是否有公告
2. 尝试手动访问网站确认状态
3. 考虑暂停使用等待网站稳定

---

**记住**：合理使用爬虫工具，遵守网站使用条款，是避免被拦截的最好方法！ 🤝