"""
数据验证模块

提供Gemini Key的验证功能
"""

import re
from typing import Dict, Any, List, Optional
import logging


class GeminiKeyValidator:
    """Gemini Key验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = logging.getLogger(__name__)
        
        # Gemini Key的验证规则
        self.validation_rules = {
            # Google AI Studio API Key
            'google_ai_studio': {
                'pattern': r'^AIza[0-9A-Za-z-_]{35}$',
                'length': 39,
                'prefix': '<PERSON>za'
            },
            
            # OpenAI格式的Key（某些Gemini服务可能使用）
            'openai_format': {
                'pattern': r'^sk-[a-zA-Z0-9]{48}$',
                'length': 51,
                'prefix': 'sk-'
            },
            
            # Google Service Key格式
            'google_service': {
                'pattern': r'^gsk_[a-zA-Z0-9]{32,64}$',
                'length_range': (36, 68),
                'prefix': 'gsk_'
            },
            
            # 通用格式（32-64位字母数字）
            'generic': {
                'pattern': r'^[a-zA-Z0-9]{32,64}$',
                'length_range': (32, 64)
            }
        }
        
        # 无效Key的特征
        self.invalid_patterns = [
            r'^test',
            r'^demo',
            r'^example',
            r'^sample',
            r'^placeholder',
            r'xxx+',
            r'000+',
            r'111+'
        ]
    
    def is_valid_key(self, key: str) -> bool:
        """
        验证Key是否有效
        
        Args:
            key: 待验证的Key
            
        Returns:
            bool: Key是否有效
        """
        if not key or not isinstance(key, str):
            return False
        
        # 基础长度检查
        if len(key) < 20 or len(key) > 100:
            return False
        
        # 检查是否匹配无效模式
        if self._is_invalid_key(key):
            return False
        
        # 检查是否匹配有效格式
        return self._matches_valid_format(key)
    
    def _is_invalid_key(self, key: str) -> bool:
        """检查是否为无效Key"""
        key_lower = key.lower()
        
        for pattern in self.invalid_patterns:
            if re.search(pattern, key_lower):
                return True
        
        return False
    
    def _matches_valid_format(self, key: str) -> bool:
        """检查是否匹配有效格式"""
        for rule_name, rule in self.validation_rules.items():
            if re.match(rule['pattern'], key):
                # 检查长度
                if 'length' in rule:
                    if len(key) == rule['length']:
                        self.logger.debug(f"Key匹配格式: {rule_name}")
                        return True
                elif 'length_range' in rule:
                    min_len, max_len = rule['length_range']
                    if min_len <= len(key) <= max_len:
                        self.logger.debug(f"Key匹配格式: {rule_name}")
                        return True
                else:
                    self.logger.debug(f"Key匹配格式: {rule_name}")
                    return True
        
        return False
    
    def get_key_type(self, key: str) -> Optional[str]:
        """
        获取Key的类型
        
        Args:
            key: Key字符串
            
        Returns:
            Optional[str]: Key类型，如果无效则返回None
        """
        if not self.is_valid_key(key):
            return None
        
        for rule_name, rule in self.validation_rules.items():
            if re.match(rule['pattern'], key):
                return rule_name
        
        return 'unknown'
    
    def validate_key_data(self, key_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证Key数据的完整性
        
        Args:
            key_data: Key数据字典
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'key': key_data.get('key', ''),
            'type': None,
            'status': key_data.get('status', 200),
            'errors': []
        }
        
        # 验证Key字段
        key = key_data.get('key', '')
        if not key:
            result['errors'].append('缺少key字段')
            return result
        
        if not isinstance(key, str):
            result['errors'].append('key字段必须是字符串')
            return result
        
        # 验证Key格式
        if not self.is_valid_key(key):
            result['errors'].append('Key格式无效')
            return result
        
        # 获取Key类型
        key_type = self.get_key_type(key)
        result['type'] = key_type
        
        # 验证状态
        status = key_data.get('status')
        if status is not None:
            if not isinstance(status, int) or status < 100 or status > 599:
                result['errors'].append('状态码无效')
                return result
        
        # 如果所有验证都通过
        if not result['errors']:
            result['valid'] = True
        
        return result
    
    def validate_key_list(self, key_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证Key列表
        
        Args:
            key_list: Key列表
            
        Returns:
            Dict[str, Any]: 验证统计结果
        """
        stats = {
            'total': len(key_list),
            'valid': 0,
            'invalid': 0,
            'status_200': 0,
            'types': {},
            'errors': []
        }
        
        for i, key_data in enumerate(key_list):
            try:
                validation_result = self.validate_key_data(key_data)
                
                if validation_result['valid']:
                    stats['valid'] += 1
                    
                    # 统计状态为200的Key
                    if validation_result['status'] == 200:
                        stats['status_200'] += 1
                    
                    # 统计Key类型
                    key_type = validation_result['type']
                    if key_type:
                        stats['types'][key_type] = stats['types'].get(key_type, 0) + 1
                else:
                    stats['invalid'] += 1
                    stats['errors'].append(f"第{i+1}个Key: {', '.join(validation_result['errors'])}")
                    
            except Exception as e:
                stats['invalid'] += 1
                stats['errors'].append(f"第{i+1}个Key验证异常: {e}")
        
        self.logger.info(f"Key验证完成: 总数={stats['total']}, 有效={stats['valid']}, 无效={stats['invalid']}, 状态200={stats['status_200']}")
        
        return stats
    
    def clean_key(self, key: str) -> str:
        """
        清理Key字符串
        
        Args:
            key: 原始Key字符串
            
        Returns:
            str: 清理后的Key字符串
        """
        if not key:
            return ''
        
        # 移除前后空白
        key = key.strip()
        
        # 移除常见的前缀/后缀
        prefixes_to_remove = ['Bearer ', 'Token ', 'API_KEY=', 'key=']
        for prefix in prefixes_to_remove:
            if key.startswith(prefix):
                key = key[len(prefix):]
        
        # 移除引号
        key = key.strip('\'"')
        
        return key
    
    def extract_keys_from_text(self, text: str) -> List[str]:
        """
        从文本中提取可能的Key
        
        Args:
            text: 文本内容
            
        Returns:
            List[str]: 提取到的Key列表
        """
        keys = []
        
        # 使用所有验证规则的模式来提取
        for rule_name, rule in self.validation_rules.items():
            pattern = rule['pattern'].replace('^', '').replace('$', '')
            matches = re.findall(pattern, text)
            
            for match in matches:
                cleaned_key = self.clean_key(match)
                if self.is_valid_key(cleaned_key):
                    keys.append(cleaned_key)
        
        # 去重
        return list(set(keys))


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        """初始化检查器"""
        self.logger = logging.getLogger(__name__)
    
    def check_data_completeness(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        检查数据完整性
        
        Args:
            data: 数据列表
            
        Returns:
            Dict[str, Any]: 完整性检查结果
        """
        result = {
            'total_records': len(data),
            'complete_records': 0,
            'incomplete_records': 0,
            'missing_fields': {},
            'field_coverage': {}
        }
        
        if not data:
            return result
        
        # 获取所有可能的字段
        all_fields = set()
        for record in data:
            if isinstance(record, dict):
                all_fields.update(record.keys())
        
        # 检查每个记录的完整性
        for record in data:
            if not isinstance(record, dict):
                result['incomplete_records'] += 1
                continue
            
            missing_count = 0
            for field in all_fields:
                if field not in record or not record[field]:
                    missing_count += 1
                    result['missing_fields'][field] = result['missing_fields'].get(field, 0) + 1
            
            if missing_count == 0:
                result['complete_records'] += 1
            else:
                result['incomplete_records'] += 1
        
        # 计算字段覆盖率
        for field in all_fields:
            present_count = len(data) - result['missing_fields'].get(field, 0)
            result['field_coverage'][field] = present_count / len(data) * 100
        
        return result
    
    def check_data_consistency(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        检查数据一致性
        
        Args:
            data: 数据列表
            
        Returns:
            Dict[str, Any]: 一致性检查结果
        """
        result = {
            'duplicate_keys': 0,
            'inconsistent_status': 0,
            'format_inconsistencies': 0,
            'unique_keys': 0
        }
        
        if not data:
            return result
        
        # 检查重复Key
        seen_keys = set()
        key_status_map = {}
        
        for record in data:
            if not isinstance(record, dict):
                continue
            
            key = record.get('key', '')
            status = record.get('status', 200)
            
            if key:
                if key in seen_keys:
                    result['duplicate_keys'] += 1
                else:
                    seen_keys.add(key)
                    result['unique_keys'] += 1
                
                # 检查同一Key的状态一致性
                if key in key_status_map:
                    if key_status_map[key] != status:
                        result['inconsistent_status'] += 1
                else:
                    key_status_map[key] = status
        
        return result