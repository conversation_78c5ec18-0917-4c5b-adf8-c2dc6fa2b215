#!/usr/bin/env python3
"""
增强版爬虫 - 支持自动分页爬取所有页面
"""

import sys
import os
import re
import time
from typing import List, Dict, Any
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import GeminiKeyCrawler, Config, DataStorage, setup_logging, ProgressTracker
from gemini_crawler.exceptions import WAFBlockedException, NetworkException


class PaginationCrawler:
    """支持分页的增强爬虫"""
    
    def __init__(self, config: Config = None):
        """初始化分页爬虫"""
        self.config = config or Config()
        self.crawler = GeminiKeyCrawler(self.config)
        self.storage = DataStorage()
        
        # 分页配置
        self.base_url = "https://geminikeyseeker.o0o.moe"
        self.page_url_template = f"{self.base_url}/?page={{page}}&status=200&sort_by=last_checked_time"
        
        # 统计信息
        self.total_pages = 0
        self.total_keys = 0
        self.failed_pages = []
        
    def detect_total_pages(self) -> int:
        """检测总页数"""
        print("🔍 检测总页数...")
        
        try:
            with self.crawler._http_client_context() as client:
                # 获取第一页来分析总页数
                response = client.get(self.page_url_template.format(page=1))
                
                # 查找最大页码
                page_links = re.findall(r'page=(\d+)', response.text)
                if page_links:
                    max_page = max(int(page) for page in page_links)
                    print(f"✅ 从页面链接检测到最大页码: {max_page}")
                    return max_page
                
                # 备用方法：查找"...47»"这样的模式
                last_page_pattern = re.search(r'\.{3}(\d+)»', response.text)
                if last_page_pattern:
                    max_page = int(last_page_pattern.group(1))
                    print(f"✅ 从分页导航检测到最大页码: {max_page}")
                    return max_page
                
                # 如果都找不到，默认尝试50页
                print("⚠️  无法检测总页数，默认尝试50页")
                return 50
                
        except Exception as e:
            print(f"❌ 检测总页数失败: {e}")
            return 50
    
    def crawl_single_page(self, page_num: int) -> List[Dict[str, Any]]:
        """爬取单个页面"""
        url = self.page_url_template.format(page=page_num)
        
        try:
            with self.crawler._http_client_context() as client:
                response = client.get(url)
                
                # 解析数据
                parsed_data = self.crawler.parser.parse_response(response)
                validated_data = self.crawler._validate_and_filter_data(parsed_data)
                
                return validated_data
                
        except Exception as e:
            print(f"❌ 第{page_num}页爬取失败: {e}")
            self.failed_pages.append(page_num)
            return []
    
    def crawl_all_pages(self, max_pages: int = None, start_page: int = 1) -> Dict[str, Any]:
        """爬取所有页面"""
        print("🚀 开始爬取所有页面...")
        
        # 检测总页数
        if max_pages is None:
            max_pages = self.detect_total_pages()
        
        self.total_pages = max_pages
        print(f"📊 计划爬取 {max_pages} 页 (从第{start_page}页开始)")
        
        # 初始化进度跟踪
        progress = ProgressTracker(max_pages - start_page + 1, "爬取进度")
        
        all_keys = []
        page_stats = []
        
        # 逐页爬取
        for page_num in range(start_page, max_pages + 1):
            print(f"\n📄 爬取第 {page_num}/{max_pages} 页...")
            
            try:
                # 爬取页面
                page_keys = self.crawl_single_page(page_num)
                
                if page_keys:
                    all_keys.extend(page_keys)
                    page_stats.append({
                        'page': page_num,
                        'keys_count': len(page_keys),
                        'status': 'success'
                    })
                    print(f"✅ 第{page_num}页: 获取 {len(page_keys)} 个Key")
                else:
                    page_stats.append({
                        'page': page_num,
                        'keys_count': 0,
                        'status': 'empty'
                    })
                    print(f"⚠️  第{page_num}页: 无数据")
                
                # 更新进度
                progress.update()
                
                # 添加延迟避免被封
                delay = self.config.get_random_delay()
                print(f"⏱️  等待 {delay:.1f}秒...")
                time.sleep(delay)
                
            except WAFBlockedException as e:
                print(f"🛡️  第{page_num}页被WAF拦截: {e}")
                self.failed_pages.append(page_num)
                page_stats.append({
                    'page': page_num,
                    'keys_count': 0,
                    'status': 'waf_blocked'
                })
                # WAF拦截时增加更长延迟
                time.sleep(10)
                
            except NetworkException as e:
                print(f"🌐 第{page_num}页网络错误: {e}")
                self.failed_pages.append(page_num)
                page_stats.append({
                    'page': page_num,
                    'keys_count': 0,
                    'status': 'network_error'
                })
                
            except Exception as e:
                print(f"❌ 第{page_num}页未知错误: {e}")
                self.failed_pages.append(page_num)
                page_stats.append({
                    'page': page_num,
                    'keys_count': 0,
                    'status': 'error'
                })
        
        progress.finish()
        
        # 去重处理
        print(f"\n🔄 数据去重处理...")
        unique_keys = self.crawler.parser.deduplicate_keys(all_keys)
        duplicate_count = len(all_keys) - len(unique_keys)
        
        self.total_keys = len(unique_keys)
        
        # 生成结果
        result = {
            'keys': unique_keys,
            'statistics': {
                'total_pages_attempted': max_pages - start_page + 1,
                'successful_pages': len([s for s in page_stats if s['status'] == 'success']),
                'failed_pages': len(self.failed_pages),
                'total_keys_found': len(all_keys),
                'unique_keys': len(unique_keys),
                'duplicate_keys': duplicate_count,
                'page_details': page_stats
            },
            'failed_pages': self.failed_pages
        }
        
        return result
    
    def retry_failed_pages(self, failed_pages: List[int]) -> List[Dict[str, Any]]:
        """重试失败的页面"""
        if not failed_pages:
            return []
        
        print(f"\n🔄 重试 {len(failed_pages)} 个失败的页面...")
        
        retry_keys = []
        for page_num in failed_pages:
            print(f"🔄 重试第 {page_num} 页...")
            
            page_keys = self.crawl_single_page(page_num)
            if page_keys:
                retry_keys.extend(page_keys)
                print(f"✅ 重试成功: 获取 {len(page_keys)} 个Key")
                # 从失败列表中移除
                if page_num in self.failed_pages:
                    self.failed_pages.remove(page_num)
            else:
                print(f"❌ 重试仍然失败")
            
            # 重试间隔
            time.sleep(self.config.get_random_delay())
        
        return retry_keys
    
    def print_summary(self, result: Dict[str, Any]):
        """打印爬取摘要"""
        stats = result['statistics']
        
        print("\n" + "=" * 60)
        print("📊 分页爬取完成摘要")
        print("=" * 60)
        
        print(f"📄 页面统计:")
        print(f"   尝试爬取: {stats['total_pages_attempted']} 页")
        print(f"   成功爬取: {stats['successful_pages']} 页")
        print(f"   失败页面: {stats['failed_pages']} 页")
        
        print(f"\n🔑 Key统计:")
        print(f"   总共发现: {stats['total_keys_found']} 个Key")
        print(f"   去重后: {stats['unique_keys']} 个Key")
        print(f"   重复Key: {stats['duplicate_keys']} 个")
        
        if result['failed_pages']:
            print(f"\n❌ 失败页面: {result['failed_pages']}")
        
        # 显示每页统计
        success_pages = [p for p in stats['page_details'] if p['status'] == 'success']
        if success_pages:
            print(f"\n📈 成功页面详情:")
            for page_info in success_pages[:10]:  # 只显示前10页
                print(f"   第{page_info['page']}页: {page_info['keys_count']} 个Key")
            if len(success_pages) > 10:
                print(f"   ... 还有 {len(success_pages) - 10} 页")


def main():
    """主函数"""
    print("🚀 Gemini Key Crawler - 分页爬取模式")
    print("=" * 50)
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建分页爬虫
    crawler = PaginationCrawler()
    
    try:
        # 爬取所有页面
        result = crawler.crawl_all_pages()
        
        # 打印摘要
        crawler.print_summary(result)
        
        # 重试失败的页面
        if result['failed_pages']:
            print(f"\n🔄 发现 {len(result['failed_pages'])} 个失败页面，尝试重试...")
            retry_keys = crawler.retry_failed_pages(result['failed_pages'])
            if retry_keys:
                result['keys'].extend(retry_keys)
                result['keys'] = crawler.crawler.parser.deduplicate_keys(result['keys'])
                print(f"✅ 重试获得 {len(retry_keys)} 个额外Key")
        
        # 保存结果
        if result['keys']:
            # 保存JSON格式
            json_file = crawler.storage.save_keys(
                result['keys'], 
                format='json',
                filename='all_pages_gemini_keys.json'
            )
            
            # 保存CSV格式
            csv_file = crawler.storage.save_keys(
                result['keys'], 
                format='csv',
                filename='all_pages_gemini_keys.csv'
            )
            
            # 保存统计报告
            report_file = crawler.storage.save_report(
                result['statistics'],
                format='json',
                filename='pagination_crawl_report.json'
            )
            
            print(f"\n💾 结果已保存:")
            print(f"   JSON文件: {json_file}")
            print(f"   CSV文件: {csv_file}")
            print(f"   统计报告: {report_file}")
            
            print(f"\n🎉 分页爬取完成！总共获取 {len(result['keys'])} 个唯一的Gemini Key")
        else:
            print("⚠️  未获取到任何Key")
    
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 爬取失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()