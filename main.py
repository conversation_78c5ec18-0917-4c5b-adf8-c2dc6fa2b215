#!/usr/bin/env python3
"""
Gemini Key Crawler - 主程序入口

用于获取 geminikeyseeker.o0o.moe 网站上状态为200的Gemini Key
支持反检测机制，处理雷池WAF保护
"""

import sys
import os
import argparse
import json
from pathlib import Path
from typing import Optional, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import (
    GeminiKeyCrawler, Config, DataStorage, setup_logging,
    CrawlerException, WAFBlockedException, NetworkException
)

# 导入分页爬虫
try:
    from crawl_all_pages import PaginationCrawler
    PAGINATION_AVAILABLE = True
except ImportError:
    PAGINATION_AVAILABLE = False


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        prog='gemini-crawler',
        description='Gemini Key Crawler - 获取状态为200的Gemini API Key',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                    # 使用默认配置运行
  %(prog)s --format json --output keys.json  # 保存为JSON格式
  %(prog)s --format csv --output keys.csv    # 保存为CSV格式
  %(prog)s --all-pages                       # 爬取所有页面
  %(prog)s --all-pages --max-pages 10        # 爬取前10页
  %(prog)s --interactive                     # 交互式配置
  %(prog)s --test-connection                 # 测试连接
  %(prog)s --list-files                      # 列出输出文件
  %(prog)s --verbose                         # 详细日志输出

更多信息请查看 README.md 文件。
        """
    )
    
    # 基础选项
    parser.add_argument(
        '--url', '-u',
        help='目标URL (默认: https://geminikeyseeker.o0o.moe/?status=200)'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='输出文件路径 (如果不指定则自动生成)'
    )
    
    parser.add_argument(
        '--format', '-f',
        choices=['json', 'csv', 'txt', 'keys-only', 'xlsx'],
        default='keys-only',
        help='输出格式 (默认: keys-only，每行一个Key)'
    )
    
    parser.add_argument(
        '--output-dir', '-d',
        default='results',
        help='输出目录 (默认: results)'
    )
    
    # 爬虫配置
    parser.add_argument(
        '--cookies', '-c',
        help='Cookie字符串 (用于认证)'
    )
    
    parser.add_argument(
        '--max-retries', '-r',
        type=int,
        help='最大重试次数 (默认: 3)'
    )
    
    parser.add_argument(
        '--delay',
        type=float,
        help='请求延迟时间(秒) (默认: 1.0-5.0随机)'
    )
    
    parser.add_argument(
        '--timeout',
        type=float,
        help='请求超时时间(秒) (默认: 30.0)'
    )
    
    # 功能选项
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='交互式配置向导'
    )
    
    parser.add_argument(
        '--test-connection', '-t',
        action='store_true',
        help='测试连接并退出'
    )
    
    parser.add_argument(
        '--list-files', '-l',
        action='store_true',
        help='列出输出目录中的文件'
    )
    
    parser.add_argument(
        '--clean-old-files',
        type=int,
        metavar='DAYS',
        help='清理N天前的旧文件'
    )

    # 分页选项
    parser.add_argument(
        '--all-pages',
        action='store_true',
        help='爬取所有页面（自动检测总页数）'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        help='最大爬取页数（配合--all-pages使用）'
    )

    parser.add_argument(
        '--start-page',
        type=int,
        default=1,
        help='起始页码（默认: 1）'
    )
    
    # 输出控制
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，只输出错误'
    )
    
    parser.add_argument(
        '--no-progress',
        action='store_true',
        help='不显示进度条'
    )
    
    parser.add_argument(
        '--no-stats',
        action='store_true',
        help='不显示统计信息'
    )
    
    # 高级选项
    parser.add_argument(
        '--config-file',
        help='配置文件路径 (JSON格式)'
    )
    
    parser.add_argument(
        '--save-config',
        help='保存当前配置到文件'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )
    
    return parser


def load_config_from_file(config_file: str) -> dict:
    """从文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return {}


def save_config_to_file(config_data: dict, config_file: str):
    """保存配置到文件"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存到: {config_file}")
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")


def interactive_config() -> dict:
    """交互式配置向导"""
    print("🔧 Gemini Key Crawler - 交互式配置向导")
    print("=" * 50)
    
    config = {}
    
    # URL配置
    url = input("目标URL (回车使用默认): ").strip()
    if url:
        config['url'] = url
    
    # 输出配置
    output_format = input("输出格式 [json/csv/txt/xlsx] (默认: json): ").strip().lower()
    if output_format in ['json', 'csv', 'txt', 'xlsx']:
        config['format'] = output_format
    
    output_dir = input("输出目录 (默认: results): ").strip()
    if output_dir:
        config['output_dir'] = output_dir
    
    # Cookie配置
    print("\n🍪 Cookie配置 (用于网站认证):")
    print("如果您有Cookie字符串，请粘贴到下面:")
    cookies = input("Cookie字符串 (可选): ").strip()
    if cookies:
        config['cookies'] = cookies
    
    # 高级配置
    print("\n⚙️ 高级配置 (可选，回车跳过):")
    
    max_retries = input("最大重试次数 (默认: 3): ").strip()
    if max_retries.isdigit():
        config['max_retries'] = int(max_retries)
    
    delay = input("请求延迟时间/秒 (默认: 1.0-5.0随机): ").strip()
    if delay.replace('.', '').isdigit():
        config['delay'] = float(delay)
    
    timeout = input("请求超时时间/秒 (默认: 30.0): ").strip()
    if timeout.replace('.', '').isdigit():
        config['timeout'] = float(timeout)
    
    # 确认配置
    print("\n📋 配置摘要:")
    for key, value in config.items():
        if key == 'cookies' and value:
            print(f"  {key}: {value[:20]}... (已截断)")
        else:
            print(f"  {key}: {value}")
    
    confirm = input("\n确认使用此配置? [Y/n]: ").strip().lower()
    if confirm in ['n', 'no']:
        print("❌ 配置已取消")
        return {}
    
    return config


def test_connection(crawler: GeminiKeyCrawler) -> bool:
    """测试连接"""
    print("🔗 测试连接到目标网站...")
    
    try:
        result = crawler.test_connection()
        
        if result['success']:
            print(f"✅ 连接测试成功!")
            print(f"   响应时间: {result['response_time']:.2f}秒")
            print(f"   状态码: {result['status_code']}")
            print(f"   内容长度: {result['content_length']} 字符")
            return True
        else:
            print(f"❌ 连接测试失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False


def list_output_files(storage: DataStorage):
    """列出输出文件"""
    print("📁 输出文件列表:")
    print("-" * 50)
    
    files = storage.list_output_files()
    
    if not files:
        print("   (无文件)")
        return
    
    for i, file_info in enumerate(files, 1):
        print(f"{i:2d}. {file_info['name']}")
        print(f"     大小: {file_info['size_formatted']}")
        print(f"     修改: {file_info['modified'][:19]}")
        print()
    
    # 显示存储统计
    storage_info = storage.get_storage_info()
    print(f"总计: {storage_info['total_files']} 个文件, {storage_info['total_size_formatted']}")


def clean_old_files(storage: DataStorage, days: int):
    """清理旧文件"""
    print(f"🧹 清理 {days} 天前的旧文件...")
    
    deleted_count = storage.clean_old_files(days)
    
    if deleted_count > 0:
        print(f"✅ 已删除 {deleted_count} 个旧文件")
    else:
        print("ℹ️  没有需要清理的文件")


def setup_crawler_config(args) -> Config:
    """设置爬虫配置"""
    config = Config()
    
    # 从命令行参数更新配置
    if args.url:
        config.target_url = args.url
    
    if args.cookies:
        config.cookies = config.parse_cookie_string(args.cookies)
    
    if args.max_retries is not None:
        config.max_retries = args.max_retries
    
    if args.delay is not None:
        config.min_delay = args.delay
        config.max_delay = args.delay
    
    if args.timeout is not None:
        config.request_timeout = args.timeout
    
    return config


def setup_progress_callback(crawler: GeminiKeyCrawler, show_progress: bool):
    """设置进度回调"""
    if not show_progress:
        return
    
    def progress_callback(progress_info):
        current = progress_info['current']
        total = progress_info['total']
        url = progress_info['url']
        keys_found = progress_info['keys_found']
        
        percentage = (current / total) * 100
        print(f"📊 进度: {current}/{total} ({percentage:.1f}%) - {keys_found} keys - {url}")
    
    def error_callback(error, context):
        url = context.get('url', 'unknown')
        print(f"⚠️  错误: {url} - {error}")
    
    crawler.set_progress_callback(progress_callback)
    crawler.set_error_callback(error_callback)


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    if args.quiet:
        log_level = 'ERROR'
    elif args.verbose:
        log_level = 'DEBUG'
    else:
        log_level = 'INFO'
    
    setup_logging(log_level)
    
    # 显示欢迎信息
    if not args.quiet:
        print("🚀 Gemini Key Crawler v1.0.0")
        print("=" * 40)
    
    try:
        # 处理配置文件
        config_data = {}
        if args.config_file:
            config_data = load_config_from_file(args.config_file)
        
        # 交互式配置
        if args.interactive:
            interactive_data = interactive_config()
            if not interactive_data:
                return 1
            config_data.update(interactive_data)
        
        # 保存配置
        if args.save_config:
            # 收集当前配置
            current_config = {
                'url': args.url or 'https://geminikeyseeker.o0o.moe/?status=200',
                'format': args.format,
                'output_dir': args.output_dir,
                'max_retries': args.max_retries or 3,
                'timeout': args.timeout or 30.0
            }
            if args.cookies:
                current_config['cookies'] = args.cookies
            if args.delay:
                current_config['delay'] = args.delay
            
            save_config_to_file(current_config, args.save_config)
            return 0
        
        # 创建存储实例
        storage = DataStorage(args.output_dir)
        
        # 处理文件管理命令
        if args.list_files:
            list_output_files(storage)
            return 0
        
        if args.clean_old_files:
            clean_old_files(storage, args.clean_old_files)
            return 0
        
        # 设置爬虫配置
        crawler_config = setup_crawler_config(args)
        
        # 应用配置文件中的设置
        if config_data:
            if 'url' in config_data:
                crawler_config.target_url = config_data['url']
            if 'cookies' in config_data:
                crawler_config.cookies = crawler_config.parse_cookie_string(config_data['cookies'])
            if 'max_retries' in config_data:
                crawler_config.max_retries = config_data['max_retries']
            if 'delay' in config_data:
                crawler_config.min_delay = config_data['delay']
                crawler_config.max_delay = config_data['delay']
            if 'timeout' in config_data:
                crawler_config.request_timeout = config_data['timeout']
        
        # 检查是否使用分页爬取
        if args.all_pages:
            if not PAGINATION_AVAILABLE:
                print("❌ 分页爬取功能不可用，请确保 crawl_all_pages.py 文件存在")
                return 1

            # 使用分页爬虫
            pagination_crawler = PaginationCrawler(crawler_config)

            if not args.quiet:
                print(f"🎯 开始分页爬取: {crawler_config.target_url}")
                print(f"📁 输出目录: {args.output_dir}")
                print(f"📄 输出格式: {args.format}")
                if args.max_pages:
                    print(f"📊 最大页数: {args.max_pages}")
                if args.start_page > 1:
                    print(f"🚀 起始页码: {args.start_page}")
                print()

            # 执行分页爬取
            result = pagination_crawler.crawl_all_pages(
                max_pages=args.max_pages,
                start_page=args.start_page
            )

            # 打印摘要
            if not args.quiet:
                pagination_crawler.print_summary(result)

            # 重试失败的页面
            if result['failed_pages'] and not args.quiet:
                print(f"\n🔄 发现 {len(result['failed_pages'])} 个失败页面，尝试重试...")
                retry_keys = pagination_crawler.retry_failed_pages(result['failed_pages'])
                if retry_keys:
                    result['keys'].extend(retry_keys)
                    result['keys'] = pagination_crawler.crawler.parser.deduplicate_keys(result['keys'])
                    if not args.quiet:
                        print(f"✅ 重试获得 {len(retry_keys)} 个额外Key")

            keys = result['keys']
            statistics = result['statistics']

        else:
            # 使用单页爬虫
            with GeminiKeyCrawler(crawler_config) as crawler:

                # 测试连接
                if args.test_connection:
                    success = test_connection(crawler)
                    return 0 if success else 1

                # 设置进度回调
                setup_progress_callback(crawler, not args.no_progress and not args.quiet)

                # 执行爬取
                if not args.quiet:
                    print(f"🎯 开始爬取: {crawler_config.target_url}")
                    print(f"📁 输出目录: {args.output_dir}")
                    print(f"📄 输出格式: {args.format}")
                    print()

                result = crawler.crawl()
                keys = result['keys']
                statistics = result['statistics']

        # 保存结果
        if keys:
            output_format = config_data.get('format', args.format)
            filepath = storage.save_keys(
                keys,
                format=output_format,
                filename=args.output
            )

            if not args.quiet:
                print(f"\n✅ 爬取完成!")
                print(f"📊 获取到 {len(keys)} 个有效的Gemini Key")
                print(f"💾 结果已保存到: {filepath}")

            # 保存统计报告
            if not args.no_stats:
                report = storage.generate_summary_report(keys, statistics)
                report_file = storage.save_report(report, format='txt')
                if not args.quiet:
                    print(f"📋 统计报告: {report_file}")

            # 显示统计信息（仅单页模式）
            if not args.all_pages and not args.no_stats and not args.quiet:
                print("\n" + "=" * 40)
                crawler.print_stats()

        else:
            print("⚠️  未获取到任何有效的Gemini Key")
            return 1
        
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return 1
        
    except WAFBlockedException as e:
        print(f"🛡️  WAF拦截: {e}")
        print("💡 建议: 尝试更换Cookie或等待一段时间后重试")
        return 1
        
    except NetworkException as e:
        print(f"🌐 网络错误: {e}")
        print("💡 建议: 检查网络连接或稍后重试")
        return 1
        
    except CrawlerException as e:
        print(f"❌ 爬虫错误: {e}")
        return 1
        
    except Exception as e:
        print(f"💥 未知错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())