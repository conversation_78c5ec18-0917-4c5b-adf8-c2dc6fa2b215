"""
HTTP客户端模块

基于requests.Session实现的HTTP客户端，包含反检测机制
专门针对雷池WAF进行优化
"""

import time
import random
import requests
from typing import Dict, Optional, Any
from urllib.parse import urljoin
import logging

from .config import Config
from .exceptions import WAFBlockedException, NetworkException, RateLimitException


class HTTPClient:
    """HTTP客户端类，包含反检测功能"""
    
    def __init__(self, config: Config):
        """
        初始化HTTP客户端
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.session = requests.Session()
        self.request_count = 0
        self.last_request_time = 0
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化Session
        self.setup_session()
    
    def setup_session(self):
        """设置Session的基础配置"""
        # 设置Cookie
        if self.config.cookies:
            self.session.cookies.update(self.config.cookies)
        
        # 设置连接池和适配器
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 禁用SSL警告
        requests.packages.urllib3.disable_warnings()
        self.session.verify = False
    
    def get_anti_detection_headers(self) -> Dict[str, str]:
        """
        生成反检测请求头
        
        Returns:
            Dict[str, str]: 包含反检测特征的请求头
        """
        # 基础请求头
        headers = self.config.get_headers()
        
        # 添加更多反检测特征
        additional_headers = {
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Ch-Ua-Platform-Version': '"15.0.0"',
            'Sec-Ch-Ua-Arch': '"x86"',
            'Sec-Ch-Ua-Bitness': '"64"',
            'Sec-Ch-Ua-Model': '""',
            'Sec-Ch-Ua-Full-Version-List': '"Not_A Brand";v="8.0.0.0", "Chromium";v="120.0.6099.109", "Google Chrome";v="120.0.6099.109"',
        }
        
        # 随机添加一些可选头
        optional_headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': self.config.base_url,
            'Referer': self.config.base_url,
        }
        
        # 随机选择是否添加可选头
        for key, value in optional_headers.items():
            if random.random() > 0.3:  # 70%概率添加
                additional_headers[key] = value
        
        headers.update(additional_headers)
        return headers
    
    def apply_request_delay(self):
        """应用请求延迟，避免频率检测"""
        current_time = time.time()
        
        # 计算需要等待的时间
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            min_interval = self.config.get_random_delay()
            
            if elapsed < min_interval:
                wait_time = min_interval - elapsed
                self.logger.debug(f"应用延迟: {wait_time:.2f}秒")
                time.sleep(wait_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def detect_waf_block(self, response: requests.Response) -> bool:
        """
        检测是否被WAF拦截
        
        Args:
            response: HTTP响应对象
            
        Returns:
            bool: 是否被WAF拦截
        """
        # 检查状态码
        if response.status_code in [403, 406, 429, 468, 503]:
            return True
        
        # 检查响应内容中的WAF特征
        content = response.text.lower()
        waf_indicators = [
            '雷池',
            'chaitin',
            'waf',
            '安全检测',
            '当前环境正在被调试',
            '客户端异常',
            'blocked',
            'forbidden',
            'access denied'
        ]
        
        for indicator in waf_indicators:
            if indicator in content:
                return True
        
        # 检查响应头
        headers = response.headers
        if 'server' in headers:
            server = headers['server'].lower()
            if any(waf in server for waf in ['chaitin', 'safeline']):
                return True
        
        return False
    
    def make_request(self, 
                    method: str = 'GET',
                    url: str = None,
                    **kwargs) -> requests.Response:
        """
        发送HTTP请求，包含完整的反检测机制
        
        Args:
            method: HTTP方法
            url: 请求URL，如果为None则使用配置中的target_url
            **kwargs: 其他requests参数
            
        Returns:
            requests.Response: HTTP响应对象
            
        Raises:
            WAFBlockedException: 被WAF拦截
            NetworkException: 网络错误
            RateLimitException: 频率限制
        """
        if url is None:
            url = self.config.target_url
        
        # 应用请求延迟
        self.apply_request_delay()
        
        # 准备请求参数
        request_kwargs = {
            'timeout': self.config.request_timeout,
            'allow_redirects': True,
            'stream': False,
        }
        request_kwargs.update(kwargs)
        
        # 设置请求头
        if 'headers' not in request_kwargs:
            request_kwargs['headers'] = self.get_anti_detection_headers()
        
        # 执行请求重试逻辑
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                self.logger.debug(f"发送请求 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {method} {url}")
                
                # 发送请求
                response = self.session.request(method, url, **request_kwargs)
                
                # 检查WAF拦截
                if self.detect_waf_block(response):
                    self.logger.warning(f"检测到WAF拦截: {response.status_code}")
                    if attempt < self.config.max_retries:
                        # 增加延迟时间
                        wait_time = self.config.retry_delay * (2 ** attempt)
                        self.logger.info(f"等待 {wait_time:.2f}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise WAFBlockedException(f"请求被WAF拦截: {response.status_code}")
                
                # 检查其他错误状态
                if response.status_code == 429:
                    raise RateLimitException("请求频率过高")
                
                # 记录成功请求
                self.logger.debug(f"请求成功: {response.status_code}")
                return response
                
            except requests.exceptions.Timeout as e:
                last_exception = NetworkException(f"请求超时: {e}")
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}): {e}")
                
            except requests.exceptions.ConnectionError as e:
                last_exception = NetworkException(f"连接错误: {e}")
                self.logger.warning(f"连接错误 (尝试 {attempt + 1}): {e}")
                
            except requests.exceptions.RequestException as e:
                last_exception = NetworkException(f"请求异常: {e}")
                self.logger.warning(f"请求异常 (尝试 {attempt + 1}): {e}")
            
            # 重试前等待
            if attempt < self.config.max_retries:
                wait_time = self.config.retry_delay * (attempt + 1)
                self.logger.info(f"等待 {wait_time:.2f}秒后重试...")
                time.sleep(wait_time)
        
        # 所有重试都失败了
        if last_exception:
            raise last_exception
        else:
            raise NetworkException("请求失败，原因未知")
    
    def get(self, url: str = None, **kwargs) -> requests.Response:
        """发送GET请求"""
        return self.make_request('GET', url, **kwargs)
    
    def post(self, url: str = None, **kwargs) -> requests.Response:
        """发送POST请求"""
        return self.make_request('POST', url, **kwargs)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取Session信息
        
        Returns:
            Dict[str, Any]: Session统计信息
        """
        return {
            'request_count': self.request_count,
            'cookies_count': len(self.session.cookies),
            'last_request_time': self.last_request_time,
            'user_agent': self.config.get_random_user_agent()[:50] + '...'
        }
    
    def close(self):
        """关闭Session"""
        if self.session:
            self.session.close()
            self.logger.debug("HTTP Session已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()