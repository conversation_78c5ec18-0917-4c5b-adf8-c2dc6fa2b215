"""
主爬虫模块

整合HTTP客户端和数据解析器，实现完整的Gemini Key爬取流程
"""

import time
from typing import List, Dict, Any, Optional, Callable
import logging
from contextlib import contextmanager

from .config import Config
from .http_client import HTTPClient
from .parsers import DataParser
from .validators import GeminiKeyValidator, DataQualityChecker
from .stats import CrawlerStats, PerformanceMonitor
from .utils import ProgressTracker
from .exceptions import (
    CrawlerException, WAFBlockedException, NetworkException,
    RateLimitException, ParseException, ValidationException
)


class GeminiKeyCrawler:
    """Gemini Key爬虫主类"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化爬虫
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or Config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.http_client = None
        self.parser = DataParser()
        self.validator = GeminiKeyValidator()
        self.quality_checker = DataQualityChecker()
        
        # 统计和监控
        self.stats = CrawlerStats()
        self.performance_monitor = PerformanceMonitor()
        
        # 状态管理
        self.is_running = False
        self.should_stop = False
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        self.logger.info("Gemini Key爬虫初始化完成")
    
    def set_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_error_callback(self, callback: Callable[[Exception, Dict[str, Any]], None]):
        """设置错误回调函数"""
        self.error_callback = callback
    
    @contextmanager
    def _http_client_context(self):
        """HTTP客户端上下文管理器"""
        self.http_client = HTTPClient(self.config)
        try:
            yield self.http_client
        finally:
            if self.http_client:
                self.http_client.close()
                self.http_client = None
    
    def crawl(self, 
             urls: Optional[List[str]] = None,
             max_retries: Optional[int] = None) -> Dict[str, Any]:
        """
        执行爬取任务
        
        Args:
            urls: 要爬取的URL列表，如果为None则使用配置中的默认URL
            max_retries: 最大重试次数，如果为None则使用配置中的值
            
        Returns:
            Dict[str, Any]: 爬取结果
        """
        if self.is_running:
            raise CrawlerException("爬虫已在运行中")
        
        self.is_running = True
        self.should_stop = False
        self.stats = CrawlerStats()  # 重置统计
        
        try:
            self.logger.info("开始执行Gemini Key爬取任务")
            
            # 准备URL列表
            if urls is None:
                urls = [self.config.target_url]
            
            # 使用HTTP客户端上下文
            with self._http_client_context() as client:
                results = self._crawl_urls(urls, max_retries or self.config.max_retries)
            
            # 完成统计
            self.stats.finish()
            
            # 生成最终结果
            final_result = self._generate_final_result(results)
            
            self.logger.info(f"爬取任务完成，共获取 {len(final_result['keys'])} 个有效Key")
            return final_result
            
        except Exception as e:
            self.logger.error(f"爬取任务失败: {e}")
            self.stats.finish()
            raise
        finally:
            self.is_running = False
    
    def _crawl_urls(self, urls: List[str], max_retries: int) -> List[Dict[str, Any]]:
        """爬取URL列表"""
        all_results = []
        progress = ProgressTracker(len(urls), "爬取进度")
        
        for i, url in enumerate(urls):
            if self.should_stop:
                self.logger.info("收到停止信号，中断爬取")
                break
            
            try:
                self.logger.info(f"爬取URL ({i+1}/{len(urls)}): {url}")
                
                # 爬取单个URL
                url_results = self._crawl_single_url(url, max_retries)
                all_results.extend(url_results)
                
                # 更新进度
                progress.update()
                
                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback({
                        'current': i + 1,
                        'total': len(urls),
                        'url': url,
                        'keys_found': len(url_results)
                    })
                
            except Exception as e:
                self.logger.error(f"爬取URL失败 {url}: {e}")
                
                # 调用错误回调
                if self.error_callback:
                    self.error_callback(e, {'url': url, 'index': i})
                
                # 继续处理下一个URL
                continue
        
        progress.finish()
        return all_results
    
    def _crawl_single_url(self, url: str, max_retries: int) -> List[Dict[str, Any]]:
        """爬取单个URL"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.debug(f"尝试爬取 {url} (第 {attempt + 1} 次)")
                
                # 发送HTTP请求
                start_time = time.time()
                response = self.http_client.get(url)
                request_time = time.time() - start_time
                
                # 记录请求成功
                self.stats.record_request_success()
                self.performance_monitor.record_request_time(request_time)
                
                # 解析响应数据
                parse_start = time.time()
                parsed_data = self._parse_response(response)
                parse_time = time.time() - parse_start
                
                # 记录解析时间
                self.performance_monitor.record_parse_time(parse_time)
                
                # 验证和过滤数据
                validated_data = self._validate_and_filter_data(parsed_data)
                
                # 记录找到的Key
                self.stats.record_keys_found(validated_data)
                
                self.logger.info(f"成功爬取 {url}，获取 {len(validated_data)} 个Key")
                return validated_data
                
            except WAFBlockedException as e:
                last_exception = e
                self.logger.warning(f"WAF拦截 {url} (尝试 {attempt + 1}): {e}")
                self.stats.record_request_failure('waf_blocked', str(e))
                
                if attempt < max_retries:
                    # WAF拦截时增加更长的等待时间
                    wait_time = self.config.retry_delay * (2 ** attempt) * 2
                    self.logger.info(f"等待 {wait_time:.1f}秒后重试...")
                    time.sleep(wait_time)
                
            except NetworkException as e:
                last_exception = e
                self.logger.warning(f"网络错误 {url} (尝试 {attempt + 1}): {e}")
                self.stats.record_request_failure('network_error', str(e))
                
                if attempt < max_retries:
                    wait_time = self.config.retry_delay * (attempt + 1)
                    self.logger.info(f"等待 {wait_time:.1f}秒后重试...")
                    time.sleep(wait_time)
                
            except ParseException as e:
                last_exception = e
                self.logger.error(f"数据解析错误 {url}: {e}")
                self.stats.record_parse_error(str(e))
                break  # 解析错误通常不需要重试
                
            except Exception as e:
                last_exception = e
                self.logger.error(f"未知错误 {url} (尝试 {attempt + 1}): {e}")
                self.stats.record_request_failure('other_error', str(e))
                
                if attempt < max_retries:
                    wait_time = self.config.retry_delay * (attempt + 1)
                    time.sleep(wait_time)
        
        # 所有重试都失败了
        if last_exception:
            raise last_exception
        else:
            raise CrawlerException(f"爬取失败: {url}")
    
    def _parse_response(self, response) -> List[Dict[str, Any]]:
        """解析HTTP响应"""
        try:
            self.logger.debug(f"解析响应数据，长度: {len(response.text)} 字符")
            
            # 使用解析器解析响应
            parsed_data = self.parser.parse_response(response)
            
            self.logger.debug(f"解析完成，获得 {len(parsed_data)} 个数据项")
            return parsed_data
            
        except Exception as e:
            raise ParseException(f"响应解析失败: {e}")
    
    def _validate_and_filter_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和过滤数据"""
        try:
            # 验证数据质量
            validation_stats = self.validator.validate_key_list(data)
            self.logger.info(f"数据验证完成: 总数={validation_stats['total']}, "
                           f"有效={validation_stats['valid']}, 状态200={validation_stats['status_200']}")
            
            # 提取状态为200的Key
            status_200_keys = self.parser.extract_status_200_keys(data)
            
            # 去重
            unique_keys = self.parser.deduplicate_keys(status_200_keys)
            duplicate_count = len(status_200_keys) - len(unique_keys)
            if duplicate_count > 0:
                self.stats.record_duplicate_keys(duplicate_count)
                self.logger.info(f"去除重复Key: {duplicate_count} 个")
            
            # 为每个Key添加验证信息
            validated_keys = []
            for key_data in unique_keys:
                validation_result = self.validator.validate_key_data(key_data)
                if validation_result['valid']:
                    key_data.update({
                        'type': validation_result['type'],
                        'validated': True,
                        'validation_time': time.time()
                    })
                    validated_keys.append(key_data)
                else:
                    self.stats.record_validation_error(f"Key验证失败: {validation_result['errors']}")
            
            return validated_keys
            
        except Exception as e:
            raise ValidationException(f"数据验证失败: {e}")
    
    def _generate_final_result(self, all_keys: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成最终结果"""
        # 全局去重
        final_keys = self.parser.deduplicate_keys(all_keys)
        
        # 数据质量检查
        quality_report = self.quality_checker.check_data_completeness(final_keys)
        consistency_report = self.quality_checker.check_data_consistency(final_keys)
        
        # 生成结果
        result = {
            'keys': final_keys,
            'statistics': self.stats.get_summary(),
            'quality_report': quality_report,
            'consistency_report': consistency_report,
            'performance_metrics': self.performance_monitor.get_performance_metrics(),
            'timestamp': time.time(),
            'config_summary': {
                'target_url': self.config.target_url,
                'max_retries': self.config.max_retries,
                'user_agents_count': len(self.config.user_agents)
            }
        }
        
        return result
    
    def stop(self):
        """停止爬虫"""
        self.should_stop = True
        self.logger.info("收到停止信号")
    
    def get_stats(self) -> CrawlerStats:
        """获取统计信息"""
        return self.stats
    
    def print_stats(self):
        """打印统计信息"""
        self.stats.print_summary()
    
    def quick_crawl(self, url: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        快速爬取单个URL
        
        Args:
            url: 要爬取的URL，如果为None则使用默认URL
            
        Returns:
            List[Dict[str, Any]]: 爬取到的Key列表
        """
        result = self.crawl([url] if url else None)
        return result['keys']
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接
        
        Returns:
            Dict[str, Any]: 连接测试结果
        """
        test_result = {
            'success': False,
            'response_time': 0,
            'status_code': None,
            'content_length': 0,
            'error': None
        }
        
        try:
            with self._http_client_context() as client:
                start_time = time.time()
                response = client.get()
                test_result['response_time'] = time.time() - start_time
                test_result['status_code'] = response.status_code
                test_result['content_length'] = len(response.text)
                test_result['success'] = True
                
                self.logger.info(f"连接测试成功: {response.status_code}, "
                               f"响应时间: {test_result['response_time']:.2f}秒")
                
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"连接测试失败: {e}")
        
        return test_result
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.is_running:
            self.stop()
        
        # 打印最终统计
        if self.stats.total_requests > 0:
            self.print_stats()