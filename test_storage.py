#!/usr/bin/env python3
"""
数据存储功能测试脚本
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler.storage import DataStorage
from gemini_crawler.utils import setup_logging


def create_test_data():
    """创建测试数据"""
    return [
        {
            "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
            "status": 200,
            "type": "google_ai_studio",
            "source": "api",
            "validated": True,
            "validation_time": 1640995200.0
        },
        {
            "key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef",
            "status": 200,
            "type": "openai_format",
            "source": "web",
            "validated": True,
            "validation_time": 1640995201.0
        },
        {
            "key": "gsk_1234567890abcdef1234567890abcdef",
            "status": 403,
            "type": "google_service",
            "source": "scrape",
            "validated": False,
            "validation_time": 1640995202.0
        },
        {
            "key": "1234567890abcdef1234567890abcdef",
            "status": 200,
            "type": "generic",
            "source": "manual",
            "validated": True,
            "validation_time": 1640995203.0
        }
    ]


def test_storage_initialization():
    """测试存储初始化"""
    print("🧪 测试存储初始化...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        
        # 检查目录是否创建
        assert Path(temp_dir).exists(), "输出目录未创建"
        assert storage.output_dir == Path(temp_dir), "输出目录路径不正确"
        assert storage.supported_formats == ['json', 'csv', 'txt', 'xlsx'], "支持格式不正确"
        
        print("✅ 存储初始化成功")
        return True


def test_json_storage():
    """测试JSON格式存储"""
    print("🧪 测试JSON格式存储...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 保存JSON格式
        filepath = storage.save_keys(test_data, format='json')
        
        # 验证文件存在
        assert os.path.exists(filepath), "JSON文件未创建"
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        assert 'keys' in saved_data, "JSON文件缺少keys字段"
        assert 'metadata' in saved_data, "JSON文件缺少metadata字段"
        assert len(saved_data['keys']) == len(test_data), "保存的Key数量不正确"
        assert saved_data['metadata']['total_count'] == len(test_data), "元数据中的总数不正确"
        
        print(f"✅ JSON格式保存成功: {os.path.basename(filepath)}")
        print(f"   文件大小: {os.path.getsize(filepath)} 字节")
        return True


def test_csv_storage():
    """测试CSV格式存储"""
    print("🧪 测试CSV格式存储...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 保存CSV格式
        filepath = storage.save_keys(test_data, format='csv')
        
        # 验证文件存在
        assert os.path.exists(filepath), "CSV文件未创建"
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含表头和数据
        assert 'key,status,type' in content, "CSV文件缺少表头"
        assert 'AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK' in content, "CSV文件缺少数据"
        
        # 计算行数（包括元数据注释和表头）
        lines = content.strip().split('\n')
        data_lines = [line for line in lines if not line.startswith('#') and line.strip()]
        assert len(data_lines) >= len(test_data), "CSV数据行数不正确"
        
        print(f"✅ CSV格式保存成功: {os.path.basename(filepath)}")
        print(f"   总行数: {len(lines)}, 数据行数: {len(data_lines) - 1}")  # -1 for header
        return True


def test_txt_storage():
    """测试TXT格式存储"""
    print("🧪 测试TXT格式存储...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 保存TXT格式
        filepath = storage.save_keys(test_data, format='txt')
        
        # 验证文件存在
        assert os.path.exists(filepath), "TXT文件未创建"
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含元数据和Key
        assert 'Gemini Key Crawler Results' in content, "TXT文件缺少标题"
        assert 'AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK' in content, "TXT文件缺少Key数据"
        
        # 检查编号
        for i in range(1, len(test_data) + 1):
            assert f"{i:4d}." in content, f"TXT文件缺少第{i}个Key的编号"
        
        print(f"✅ TXT格式保存成功: {os.path.basename(filepath)}")
        print(f"   文件大小: {os.path.getsize(filepath)} 字节")
        return True


def test_report_generation():
    """测试报告生成"""
    print("🧪 测试报告生成...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 创建测试统计数据
        test_stats = {
            'duration': 10.5,
            'requests': {
                'total': 5,
                'successful': 4,
                'failed': 1,
                'success_rate': '80.0%'
            },
            'keys': {
                'total_found': 4,
                'valid': 3,
                'invalid': 1,
                'status_200': 3,
                'duplicates': 0,
                'types': {
                    'google_ai_studio': 1,
                    'openai_format': 1,
                    'google_service': 1,
                    'generic': 1
                }
            },
            'errors': {
                'waf_blocked': 1,
                'network_errors': 0,
                'parse_errors': 0,
                'validation_errors': 0
            }
        }
        
        # 生成摘要报告
        report = storage.generate_summary_report(test_data, test_stats)
        
        # 验证报告结构
        assert 'summary' in report, "报告缺少summary字段"
        assert 'distributions' in report, "报告缺少distributions字段"
        assert 'statistics' in report, "报告缺少statistics字段"
        assert report['summary']['total_keys'] == len(test_data), "报告中的Key总数不正确"
        
        # 保存报告
        report_path = storage.save_report(report, format='json')
        assert os.path.exists(report_path), "报告文件未创建"
        
        # 保存TXT格式报告
        txt_report_path = storage.save_report(test_stats, format='txt')
        assert os.path.exists(txt_report_path), "TXT报告文件未创建"
        
        print(f"✅ 报告生成成功:")
        print(f"   JSON报告: {os.path.basename(report_path)}")
        print(f"   TXT报告: {os.path.basename(txt_report_path)}")
        return True


def test_file_management():
    """测试文件管理功能"""
    print("🧪 测试文件管理功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 创建几个测试文件
        files_created = []
        for format in ['json', 'csv', 'txt']:
            filepath = storage.save_keys(test_data, format=format)
            files_created.append(filepath)
        
        # 测试文件列表功能
        file_list = storage.list_output_files()
        assert len(file_list) == 3, f"文件列表数量不正确: {len(file_list)}"
        
        # 验证文件信息
        for file_info in file_list:
            assert 'name' in file_info, "文件信息缺少name字段"
            assert 'size' in file_info, "文件信息缺少size字段"
            assert 'modified' in file_info, "文件信息缺少modified字段"
            assert file_info['size'] > 0, "文件大小应该大于0"
        
        # 测试存储信息
        storage_info = storage.get_storage_info()
        assert 'total_files' in storage_info, "存储信息缺少total_files字段"
        assert 'total_size' in storage_info, "存储信息缺少total_size字段"
        assert storage_info['total_files'] == 3, "存储信息中的文件数量不正确"
        
        print(f"✅ 文件管理功能测试成功:")
        print(f"   文件总数: {storage_info['total_files']}")
        print(f"   总大小: {storage_info['total_size_formatted']}")
        print(f"   支持格式: {storage_info['supported_formats']}")
        return True


def test_backup_functionality():
    """测试备份功能"""
    print("🧪 测试备份功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 创建一个文件
        original_file = storage.save_keys(test_data, format='json', filename='test.json')
        
        # 备份文件
        backup_file = storage.backup_existing_file(original_file)
        
        # 验证备份文件存在
        assert backup_file is not None, "备份文件路径为None"
        assert os.path.exists(backup_file), "备份文件不存在"
        
        # 验证备份文件内容与原文件相同
        with open(original_file, 'r') as f1, open(backup_file, 'r') as f2:
            assert f1.read() == f2.read(), "备份文件内容与原文件不同"
        
        # 测试不存在文件的备份
        non_existent_backup = storage.backup_existing_file('non_existent.txt')
        assert non_existent_backup is None, "不存在文件的备份应该返回None"
        
        print(f"✅ 备份功能测试成功:")
        print(f"   原文件: {os.path.basename(original_file)}")
        print(f"   备份文件: {os.path.basename(backup_file)}")
        return True


def test_error_handling():
    """测试错误处理"""
    print("🧪 测试错误处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        
        # 测试不支持的格式
        try:
            storage.save_keys([], format='unsupported')
            assert False, "应该抛出StorageException"
        except Exception as e:
            assert '不支持的格式' in str(e), "错误信息不正确"
        
        # 测试空数据
        result = storage.save_keys([], format='json')
        assert result == "", "空数据应该返回空字符串"
        
        print("✅ 错误处理测试成功")
        return True


def test_custom_filename():
    """测试自定义文件名"""
    print("🧪 测试自定义文件名...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = DataStorage(temp_dir)
        test_data = create_test_data()
        
        # 使用自定义文件名
        custom_filename = "my_custom_keys.json"
        filepath = storage.save_keys(test_data, format='json', filename=custom_filename)
        
        # 验证文件名
        assert os.path.basename(filepath) == custom_filename, "自定义文件名不正确"
        assert os.path.exists(filepath), "自定义文件名的文件不存在"
        
        print(f"✅ 自定义文件名测试成功: {custom_filename}")
        return True


def main():
    """主测试函数"""
    print("🚀 开始数据存储功能测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging('INFO')
    
    try:
        # 测试存储初始化
        if not test_storage_initialization():
            print("\n❌ 存储初始化测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试JSON存储
        if not test_json_storage():
            print("\n❌ JSON存储测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试CSV存储
        if not test_csv_storage():
            print("\n❌ CSV存储测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试TXT存储
        if not test_txt_storage():
            print("\n❌ TXT存储测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试报告生成
        if not test_report_generation():
            print("\n❌ 报告生成测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试文件管理
        if not test_file_management():
            print("\n❌ 文件管理测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试备份功能
        if not test_backup_functionality():
            print("\n❌ 备份功能测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试错误处理
        if not test_error_handling():
            print("\n❌ 错误处理测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试自定义文件名
        if not test_custom_filename():
            print("\n❌ 自定义文件名测试失败")
            return False
        
        print("\n🎉 所有测试通过！")
        print("✅ 存储初始化功能正常")
        print("✅ JSON格式存储正常")
        print("✅ CSV格式存储正常")
        print("✅ TXT格式存储正常")
        print("✅ 报告生成功能正常")
        print("✅ 文件管理功能正常")
        print("✅ 备份功能正常")
        print("✅ 错误处理机制完整")
        print("✅ 自定义文件名功能正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)