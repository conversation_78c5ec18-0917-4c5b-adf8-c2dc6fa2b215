"""
数据解析器模块

支持HTML和JSON格式的数据解析，专门用于提取Gemini Key
"""

import re
import json
from typing import List, Dict, Any, Optional, Union
from bs4 import BeautifulSoup
import logging

from .exceptions import ParseException
from .validators import GeminiKeyValidator


class DataParser:
    """数据解析器类"""
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)
        self.validator = GeminiKeyValidator()
        
        # Gemini Key的常见格式模式
        self.key_patterns = [
            # Google AI Studio API Key格式
            r'AIza[0-9A-Za-z-_]{35}',
            # Gemini API Key格式
            r'sk-[a-zA-Z0-9]{48}',
            # 其他可能的格式
            r'gsk_[a-zA-Z0-9]{32,64}',
            r'[a-zA-Z0-9]{32,64}'  # 通用格式
        ]
    
    def parse_response(self, response) -> List[Dict[str, Any]]:
        """
        解析HTTP响应，自动检测格式并提取数据
        
        Args:
            response: HTTP响应对象
            
        Returns:
            List[Dict[str, Any]]: 解析后的数据列表
            
        Raises:
            ParseException: 解析失败
        """
        try:
            content_type = response.headers.get('Content-Type', '').lower()
            
            if 'application/json' in content_type:
                self.logger.info("检测到JSON格式，使用JSON解析器")
                return self.parse_json(response.text)
            
            elif 'text/html' in content_type or 'text/plain' in content_type:
                self.logger.info("检测到HTML/文本格式，使用HTML解析器")
                return self.parse_html(response.text)
            
            else:
                # 尝试自动检测格式
                self.logger.info("未知格式，尝试自动检测")
                return self.auto_detect_and_parse(response.text)
                
        except Exception as e:
            raise ParseException(f"响应解析失败: {e}")
    
    def parse_json(self, json_text: str) -> List[Dict[str, Any]]:
        """
        解析JSON格式数据
        
        Args:
            json_text: JSON字符串
            
        Returns:
            List[Dict[str, Any]]: 解析后的数据列表
        """
        try:
            data = json.loads(json_text)
            
            # 处理不同的JSON结构
            if isinstance(data, list):
                return self._extract_keys_from_list(data)
            
            elif isinstance(data, dict):
                return self._extract_keys_from_dict(data)
            
            else:
                self.logger.warning(f"未知的JSON数据类型: {type(data)}")
                return []
                
        except json.JSONDecodeError as e:
            raise ParseException(f"JSON解析失败: {e}")
    
    def parse_html(self, html_text: str) -> List[Dict[str, Any]]:
        """
        解析HTML格式数据
        
        Args:
            html_text: HTML字符串
            
        Returns:
            List[Dict[str, Any]]: 解析后的数据列表
        """
        try:
            soup = BeautifulSoup(html_text, 'html.parser')
            results = []
            
            # 方法1: 查找表格数据
            tables = soup.find_all('table')
            for table in tables:
                table_data = self._parse_table(table)
                results.extend(table_data)
            
            # 方法2: 查找JavaScript中的JSON数据
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    script_data = self._extract_json_from_script(script.string)
                    results.extend(script_data)
            
            # 方法3: 查找列表数据
            lists = soup.find_all(['ul', 'ol'])
            for list_elem in lists:
                list_data = self._parse_list(list_elem)
                results.extend(list_data)
            
            # 方法4: 使用正则表达式直接提取Key
            text_keys = self._extract_keys_from_text(html_text)
            results.extend(text_keys)
            
            return results
            
        except Exception as e:
            raise ParseException(f"HTML解析失败: {e}")
    
    def auto_detect_and_parse(self, text: str) -> List[Dict[str, Any]]:
        """
        自动检测格式并解析
        
        Args:
            text: 文本内容
            
        Returns:
            List[Dict[str, Any]]: 解析后的数据列表
        """
        results = []
        
        # 尝试JSON解析
        try:
            json_data = json.loads(text)
            if isinstance(json_data, (list, dict)):
                self.logger.info("自动检测：JSON格式")
                return self.parse_json(text)
        except json.JSONDecodeError:
            pass
        
        # 尝试HTML解析
        if '<html' in text.lower() or '<table' in text.lower():
            self.logger.info("自动检测：HTML格式")
            return self.parse_html(text)
        
        # 直接文本提取
        self.logger.info("自动检测：纯文本格式")
        return self._extract_keys_from_text(text)
    
    def _extract_keys_from_list(self, data_list: List[Any]) -> List[Dict[str, Any]]:
        """从列表中提取Key数据"""
        results = []
        
        for item in data_list:
            if isinstance(item, dict):
                key_data = self._extract_key_from_dict(item)
                if key_data:
                    results.append(key_data)
            elif isinstance(item, str):
                key_data = self._extract_key_from_string(item)
                if key_data:
                    results.append(key_data)
        
        return results
    
    def _extract_keys_from_dict(self, data_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从字典中提取Key数据"""
        results = []
        
        # 检查是否有keys字段
        if 'keys' in data_dict and isinstance(data_dict['keys'], list):
            return self._extract_keys_from_list(data_dict['keys'])
        
        # 检查是否有data字段
        if 'data' in data_dict and isinstance(data_dict['data'], list):
            return self._extract_keys_from_list(data_dict['data'])
        
        # 检查是否有items字段
        if 'items' in data_dict and isinstance(data_dict['items'], list):
            return self._extract_keys_from_list(data_dict['items'])
        
        # 直接从字典中提取
        key_data = self._extract_key_from_dict(data_dict)
        if key_data:
            results.append(key_data)
        
        return results
    
    def _extract_key_from_dict(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从字典项中提取Key信息"""
        # 常见的字段名
        key_fields = ['key', 'api_key', 'apikey', 'token', 'access_token']
        status_fields = ['status', 'state', 'active', 'valid']
        
        key_value = None
        status_value = None
        
        # 查找Key字段
        for field in key_fields:
            if field in item:
                key_value = str(item[field])
                break
        
        # 查找状态字段
        for field in status_fields:
            if field in item:
                status_value = item[field]
                break
        
        # 如果没有找到明确的Key字段，尝试从所有字符串字段中找
        if not key_value:
            for key, value in item.items():
                if isinstance(value, str) and self._is_potential_key(value):
                    key_value = value
                    break
        
        if key_value and self.validator.is_valid_key(key_value):
            return {
                'key': key_value,
                'status': self._normalize_status(status_value),
                'source': 'dict',
                'raw_data': item
            }
        
        return None
    
    def _extract_key_from_string(self, text: str) -> Optional[Dict[str, Any]]:
        """从字符串中提取Key信息"""
        # 使用正则表达式匹配Key格式
        for pattern in self.key_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if self.validator.is_valid_key(match):
                    return {
                        'key': match,
                        'status': 200,  # 默认状态
                        'source': 'string',
                        'raw_data': text
                    }
        
        return None
    
    def _parse_table(self, table) -> List[Dict[str, Any]]:
        """解析HTML表格"""
        results = []
        
        try:
            rows = table.find_all('tr')
            if not rows:
                return results
            
            # 获取表头
            headers = []
            header_row = rows[0]
            for th in header_row.find_all(['th', 'td']):
                headers.append(th.get_text(strip=True).lower())
            
            # 解析数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) == len(headers):
                    row_data = {}
                    for i, cell in enumerate(cells):
                        row_data[headers[i]] = cell.get_text(strip=True)
                    
                    key_data = self._extract_key_from_dict(row_data)
                    if key_data:
                        results.append(key_data)
        
        except Exception as e:
            self.logger.warning(f"表格解析失败: {e}")
        
        return results
    
    def _parse_list(self, list_elem) -> List[Dict[str, Any]]:
        """解析HTML列表"""
        results = []
        
        try:
            items = list_elem.find_all('li')
            for item in items:
                text = item.get_text(strip=True)
                key_data = self._extract_key_from_string(text)
                if key_data:
                    results.append(key_data)
        
        except Exception as e:
            self.logger.warning(f"列表解析失败: {e}")
        
        return results
    
    def _extract_json_from_script(self, script_text: str) -> List[Dict[str, Any]]:
        """从JavaScript代码中提取JSON数据"""
        results = []
        
        try:
            # 查找JSON对象
            json_patterns = [
                r'var\s+\w+\s*=\s*(\{.*?\});',
                r'const\s+\w+\s*=\s*(\{.*?\});',
                r'let\s+\w+\s*=\s*(\{.*?\});',
                r'window\.\w+\s*=\s*(\{.*?\});',
                r'(\{[^{}]*"key"[^{}]*\})',  # 包含key字段的对象
                r'(\[[^\[\]]*\{[^{}]*"key"[^{}]*\}[^\[\]]*\])'  # 包含key的数组
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, script_text, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, dict):
                            key_data = self._extract_key_from_dict(data)
                            if key_data:
                                results.append(key_data)
                        elif isinstance(data, list):
                            list_results = self._extract_keys_from_list(data)
                            results.extend(list_results)
                    except json.JSONDecodeError:
                        continue
        
        except Exception as e:
            self.logger.warning(f"脚本JSON提取失败: {e}")
        
        return results
    
    def _extract_keys_from_text(self, text: str) -> List[Dict[str, Any]]:
        """从纯文本中提取Key"""
        results = []
        
        for pattern in self.key_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if self.validator.is_valid_key(match):
                    results.append({
                        'key': match,
                        'status': 200,  # 默认状态
                        'source': 'text',
                        'raw_data': match
                    })
        
        return results
    
    def _is_potential_key(self, text: str) -> bool:
        """判断文本是否可能是Key"""
        if not text or len(text) < 20:
            return False
        
        # 检查是否匹配Key格式
        for pattern in self.key_patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def _normalize_status(self, status: Any) -> int:
        """标准化状态值"""
        if status is None:
            return 200  # 默认状态
        
        if isinstance(status, int):
            return status
        
        if isinstance(status, str):
            status_lower = status.lower()
            if status_lower in ['active', 'valid', 'ok', '200', 'working']:
                return 200
            elif status_lower in ['inactive', 'invalid', 'error', '403', 'blocked']:
                return 403
            elif status_lower in ['expired', 'timeout', '408']:
                return 408
            else:
                # 尝试转换为数字
                try:
                    return int(status)
                except ValueError:
                    return 200
        
        return 200
    
    def extract_status_200_keys(self, parsed_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取状态为200的Gemini Key
        
        Args:
            parsed_data: 解析后的数据列表
            
        Returns:
            List[Dict[str, Any]]: 状态为200的Key列表
        """
        status_200_keys = []
        
        for item in parsed_data:
            if item.get('status') == 200:
                # 进一步验证Key的有效性
                if self.validator.is_valid_key(item.get('key', '')):
                    status_200_keys.append(item)
        
        self.logger.info(f"提取到 {len(status_200_keys)} 个状态为200的有效Key")
        return status_200_keys
    
    def deduplicate_keys(self, keys: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重Key列表
        
        Args:
            keys: Key列表
            
        Returns:
            List[Dict[str, Any]]: 去重后的Key列表
        """
        seen_keys = set()
        unique_keys = []
        
        for key_data in keys:
            key = key_data.get('key', '')
            if key and key not in seen_keys:
                seen_keys.add(key)
                unique_keys.append(key_data)
        
        self.logger.info(f"去重前: {len(keys)} 个Key，去重后: {len(unique_keys)} 个Key")
        return unique_keys