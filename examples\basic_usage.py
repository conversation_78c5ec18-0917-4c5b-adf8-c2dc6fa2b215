#!/usr/bin/env python3
"""
Gemini Key Crawler - 基础使用示例

演示如何在Python代码中使用爬虫库
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gemini_crawler import GeminiKeyCrawler, Config, DataStorage, setup_logging


def example_basic_usage():
    """基础使用示例"""
    print("🚀 基础使用示例")
    print("-" * 30)
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建爬虫实例
    crawler = GeminiKeyCrawler()
    
    # 测试连接
    print("🔗 测试连接...")
    connection_result = crawler.test_connection()
    if connection_result['success']:
        print(f"✅ 连接成功，响应时间: {connection_result['response_time']:.2f}秒")
    else:
        print(f"❌ 连接失败: {connection_result['error']}")
        return
    
    # 执行爬取
    print("\n🎯 开始爬取...")
    try:
        result = crawler.crawl()
        keys = result['keys']
        
        print(f"✅ 爬取完成，获取到 {len(keys)} 个Key")
        
        # 显示前几个Key
        for i, key_data in enumerate(keys[:3], 1):
            print(f"  {i}. {key_data['key'][:30]}... (状态: {key_data['status']})")
        
        if len(keys) > 3:
            print(f"  ... 还有 {len(keys) - 3} 个Key")
        
        # 保存结果
        storage = DataStorage()
        filepath = storage.save_keys(keys, format='json')
        print(f"💾 结果已保存到: {filepath}")
        
        # 显示统计信息
        print("\n📊 统计信息:")
        crawler.print_stats()
        
    except Exception as e:
        print(f"❌ 爬取失败: {e}")


def example_custom_config():
    """自定义配置示例"""
    print("\n🔧 自定义配置示例")
    print("-" * 30)
    
    # 创建自定义配置
    config = Config()
    config.max_retries = 5
    config.min_delay = 2.0
    config.max_delay = 4.0
    config.request_timeout = 60.0
    
    print(f"📋 配置信息:")
    print(f"  最大重试: {config.max_retries}")
    print(f"  延迟范围: {config.min_delay}-{config.max_delay}秒")
    print(f"  超时时间: {config.request_timeout}秒")
    
    # 使用自定义配置创建爬虫
    with GeminiKeyCrawler(config) as crawler:
        print("\n🎯 使用自定义配置爬取...")
        
        # 设置进度回调
        def progress_callback(info):
            print(f"📊 进度: {info['current']}/{info['total']} - 发现 {info['keys_found']} 个Key")
        
        crawler.set_progress_callback(progress_callback)
        
        try:
            result = crawler.crawl()
            print(f"✅ 自定义配置爬取完成，获取到 {len(result['keys'])} 个Key")
        except Exception as e:
            print(f"❌ 自定义配置爬取失败: {e}")


def example_multiple_formats():
    """多格式保存示例"""
    print("\n📁 多格式保存示例")
    print("-" * 30)
    
    # 模拟一些Key数据
    sample_keys = [
        {
            "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
            "status": 200,
            "type": "google_ai_studio",
            "source": "api",
            "validated": True
        },
        {
            "key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef",
            "status": 200,
            "type": "openai_format",
            "source": "web",
            "validated": True
        }
    ]
    
    # 创建存储实例
    storage = DataStorage("examples/output")
    
    # 保存为不同格式
    formats = ['json', 'csv', 'txt']
    for format in formats:
        try:
            filepath = storage.save_keys(sample_keys, format=format)
            print(f"✅ {format.upper()}格式保存成功: {os.path.basename(filepath)}")
        except Exception as e:
            print(f"❌ {format.upper()}格式保存失败: {e}")
    
    # 显示存储信息
    storage_info = storage.get_storage_info()
    print(f"\n📊 存储统计:")
    print(f"  文件总数: {storage_info['total_files']}")
    print(f"  总大小: {storage_info['total_size_formatted']}")


def example_error_handling():
    """错误处理示例"""
    print("\n⚠️  错误处理示例")
    print("-" * 30)
    
    from gemini_crawler.exceptions import WAFBlockedException, NetworkException
    
    # 创建爬虫
    crawler = GeminiKeyCrawler()
    
    try:
        # 这里可能会遇到各种错误
        result = crawler.crawl()
        print(f"✅ 爬取成功: {len(result['keys'])} 个Key")
        
    except WAFBlockedException as e:
        print(f"🛡️  WAF拦截: {e}")
        print("💡 建议: 检查Cookie或增加延迟时间")
        
    except NetworkException as e:
        print(f"🌐 网络错误: {e}")
        print("💡 建议: 检查网络连接")
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        print("💡 建议: 查看详细日志或联系支持")


def main():
    """主函数"""
    print("📖 Gemini Key Crawler - 使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_custom_config()
    example_multiple_formats()
    example_error_handling()
    
    print("\n🎉 所有示例运行完成!")
    print("💡 更多信息请查看 README.md 文档")


if __name__ == "__main__":
    main()