#!/usr/bin/env python3
"""
分析网站分页机制
"""

import sys
import os
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler import GeminiKeyCrawler, Config, setup_logging
from bs4 import BeautifulSoup


def analyze_pagination():
    """分析网站的分页机制"""
    print("🔍 分析网站分页机制...")
    
    setup_logging('INFO')
    
    # 创建爬虫
    crawler = GeminiKeyCrawler()
    
    try:
        with crawler._http_client_context() as client:
            # 获取第一页内容
            print("📄 获取第一页内容...")
            response = client.get()
            
            print(f"✅ 响应状态: {response.status_code}")
            print(f"📏 内容长度: {len(response.text)} 字符")
            
            # 保存完整HTML用于分析
            with open('page_analysis.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("💾 完整HTML已保存到 page_analysis.html")
            
            # 使用BeautifulSoup解析
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找分页相关元素
            print("\n🔍 查找分页元素...")
            
            # 1. 查找包含"页"、"page"、"next"等关键词的元素
            pagination_keywords = ['page', 'next', 'prev', 'pagination', '页', '下一页', '上一页', '首页', '末页']
            pagination_elements = []
            
            for keyword in pagination_keywords:
                # 查找文本包含关键词的元素
                elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
                for element in elements:
                    parent = element.parent if element.parent else element
                    pagination_elements.append((keyword, parent))
            
            if pagination_elements:
                print(f"✅ 找到 {len(pagination_elements)} 个可能的分页元素:")
                for keyword, element in pagination_elements[:10]:  # 只显示前10个
                    print(f"   关键词: {keyword}")
                    print(f"   标签: {element.name}")
                    print(f"   文本: {element.get_text(strip=True)[:50]}...")
                    if element.get('href'):
                        print(f"   链接: {element.get('href')}")
                    print()
            
            # 2. 查找常见的分页CSS类名
            pagination_classes = ['pagination', 'pager', 'page-nav', 'page-numbers', 'paginate']
            for class_name in pagination_classes:
                elements = soup.find_all(class_=re.compile(class_name, re.IGNORECASE))
                if elements:
                    print(f"✅ 找到CSS类 '{class_name}' 的元素:")
                    for element in elements:
                        print(f"   标签: {element.name}")
                        print(f"   类名: {element.get('class')}")
                        print(f"   内容: {element.get_text(strip=True)[:100]}...")
                        print()
            
            # 3. 查找数字链接（可能是页码）
            number_links = soup.find_all('a', href=True, text=re.compile(r'^\d+$'))
            if number_links:
                print(f"✅ 找到 {len(number_links)} 个数字链接（可能是页码）:")
                for link in number_links[:5]:  # 只显示前5个
                    print(f"   页码: {link.get_text()}")
                    print(f"   链接: {link.get('href')}")
                    print()
            
            # 4. 查找URL参数模式
            all_links = soup.find_all('a', href=True)
            page_param_links = []
            for link in all_links:
                href = link.get('href')
                if re.search(r'[?&](page|p|offset|start)=\d+', href, re.IGNORECASE):
                    page_param_links.append(link)
            
            if page_param_links:
                print(f"✅ 找到 {len(page_param_links)} 个包含分页参数的链接:")
                for link in page_param_links[:5]:  # 只显示前5个
                    print(f"   文本: {link.get_text(strip=True)}")
                    print(f"   链接: {link.get('href')}")
                    print()
            
            # 5. 查找JavaScript分页相关代码
            scripts = soup.find_all('script')
            js_pagination_patterns = [
                r'page\s*[=:]\s*\d+',
                r'currentPage',
                r'totalPages',
                r'loadMore',
                r'nextPage',
                r'pagination'
            ]
            
            js_pagination_found = []
            for script in scripts:
                if script.string:
                    for pattern in js_pagination_patterns:
                        matches = re.findall(pattern, script.string, re.IGNORECASE)
                        if matches:
                            js_pagination_found.extend(matches)
            
            if js_pagination_found:
                print(f"✅ 在JavaScript中找到分页相关代码:")
                for match in set(js_pagination_found)[:10]:  # 去重并只显示前10个
                    print(f"   {match}")
                print()
            
            # 6. 分析当前页面的Key数量和可能的总数提示
            print("📊 分析当前页面数据...")
            
            # 查找可能显示总数的元素
            total_indicators = soup.find_all(text=re.compile(r'共\s*\d+|total\s*\d+|总计\s*\d+', re.IGNORECASE))
            if total_indicators:
                print("✅ 找到可能的总数指示:")
                for indicator in total_indicators:
                    print(f"   {indicator.strip()}")
                print()
            
            # 统计当前页面的Key数量
            key_patterns = [
                r'AIza[0-9A-Za-z-_]{35}',  # Google AI Studio
                r'sk-[a-zA-Z0-9]{48}',     # OpenAI format
                r'gsk_[a-zA-Z0-9]{32,64}', # Google Service
            ]
            
            total_keys_found = 0
            for pattern in key_patterns:
                matches = re.findall(pattern, response.text)
                total_keys_found += len(matches)
                if matches:
                    print(f"✅ 找到 {len(matches)} 个 {pattern} 格式的Key")
            
            print(f"📊 当前页面总共找到 {total_keys_found} 个Key")
            
            # 7. 尝试检测分页URL模式
            print("\n🔍 尝试检测分页URL模式...")
            base_url = "https://geminikeyseeker.o0o.moe/?status=200"
            
            # 常见的分页URL模式
            pagination_patterns = [
                f"{base_url}&page=2",
                f"{base_url}&p=2", 
                "https://geminikeyseeker.o0o.moe/?status=200&page=2",
                "https://geminikeyseeker.o0o.moe/?page=2&status=200",
                "https://geminikeyseeker.o0o.moe/page/2?status=200",
            ]
            
            print("🧪 测试可能的分页URL模式:")
            for pattern in pagination_patterns:
                print(f"   {pattern}")
            
            return True
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


if __name__ == "__main__":
    analyze_pagination()