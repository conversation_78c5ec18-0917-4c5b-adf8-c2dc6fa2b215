#!/usr/bin/env python3
"""
HTTP客户端测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler.config import Config
from gemini_crawler.http_client import HTTPClient
from gemini_crawler.utils import setup_logging
from gemini_crawler.exceptions import WAFBlockedException, NetworkException


def test_http_client():
    """测试HTTP客户端功能"""
    print("🧪 测试HTTP客户端...")
    
    # 设置日志
    logger = setup_logging('DEBUG')
    
    # 创建配置
    config = Config()
    print(f"✅ 配置创建成功: {config.target_url}")
    
    # 创建HTTP客户端
    with HTTPClient(config) as client:
        print(f"✅ HTTP客户端创建成功")
        
        # 测试Session信息
        session_info = client.get_session_info()
        print(f"✅ Session信息: {session_info}")
        
        # 测试请求头生成
        headers = client.get_anti_detection_headers()
        print(f"✅ 反检测请求头数量: {len(headers)}")
        print(f"   User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
        print(f"   包含Sec-Ch-Ua: {'Sec-Ch-Ua' in headers}")
        
        # 测试延迟机制
        print("🕐 测试延迟机制...")
        import time
        start_time = time.time()
        client.apply_request_delay()
        client.apply_request_delay()
        elapsed = time.time() - start_time
        print(f"✅ 延迟机制工作正常: {elapsed:.2f}秒")
        
        # 测试WAF检测（模拟响应）
        print("🛡️ 测试WAF检测...")
        
        # 模拟正常响应
        class MockResponse:
            def __init__(self, status_code, text, headers=None):
                self.status_code = status_code
                self.text = text
                self.headers = headers or {}
        
        normal_response = MockResponse(200, "正常内容")
        waf_response = MockResponse(403, "雷池WAF拦截")
        
        print(f"✅ 正常响应检测: {not client.detect_waf_block(normal_response)}")
        print(f"✅ WAF响应检测: {client.detect_waf_block(waf_response)}")
        
        # 尝试实际请求（可能会被WAF拦截）
        print("🌐 尝试实际请求...")
        try:
            # 先测试一个简单的请求
            test_url = "https://httpbin.org/get"
            response = client.get(test_url)
            print(f"✅ 测试请求成功: {response.status_code}")
            
            # 尝试目标网站（可能被拦截）
            print("🎯 尝试访问目标网站...")
            response = client.get()
            print(f"✅ 目标网站访问成功: {response.status_code}")
            print(f"   响应长度: {len(response.text)} 字符")
            
        except WAFBlockedException as e:
            print(f"⚠️ 被WAF拦截（预期行为）: {e}")
        except NetworkException as e:
            print(f"⚠️ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    print("✅ HTTP客户端测试完成")
    return True


def test_anti_detection_features():
    """测试反检测功能"""
    print("\n🕵️ 测试反检测功能...")
    
    config = Config()
    client = HTTPClient(config)
    
    # 测试多次请求头生成的随机性
    headers_list = []
    for i in range(5):
        headers = client.get_anti_detection_headers()
        headers_list.append(headers['User-Agent'])
    
    # 检查User-Agent的多样性
    unique_uas = set(headers_list)
    print(f"✅ User-Agent多样性: {len(unique_uas)}/{len(headers_list)}")
    
    # 检查请求头完整性
    sample_headers = client.get_anti_detection_headers()
    required_headers = ['User-Agent', 'Accept', 'Accept-Language', 'Accept-Encoding']
    missing_headers = [h for h in required_headers if h not in sample_headers]
    
    if not missing_headers:
        print("✅ 必需请求头完整")
    else:
        print(f"❌ 缺少请求头: {missing_headers}")
        return False
    
    # 检查反检测特征
    detection_headers = ['Sec-Ch-Ua', 'Sec-Ch-Ua-Mobile', 'Sec-Ch-Ua-Platform']
    present_headers = [h for h in detection_headers if h in sample_headers]
    print(f"✅ 反检测请求头: {len(present_headers)}/{len(detection_headers)}")
    
    client.close()
    return True


def main():
    """主测试函数"""
    print("🚀 开始HTTP客户端功能测试")
    print("=" * 60)
    
    try:
        # 测试HTTP客户端基础功能
        if not test_http_client():
            print("\n❌ HTTP客户端测试失败")
            return False
        
        # 测试反检测功能
        if not test_anti_detection_features():
            print("\n❌ 反检测功能测试失败")
            return False
        
        print("\n🎉 所有测试通过！")
        print("✅ HTTP客户端功能正常")
        print("✅ 反检测机制工作正常")
        print("✅ 错误处理机制完整")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)