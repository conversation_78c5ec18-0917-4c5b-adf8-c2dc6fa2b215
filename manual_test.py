#!/usr/bin/env python3
"""
手动测试配置模块功能
"""

# 模拟Config类的核心功能
import os
import random

def parse_cookie_string(cookie_string):
    """解析Cookie字符串"""
    if not cookie_string:
        return {}
    
    cookies = {}
    try:
        cookie_pairs = cookie_string.split(';')
        for pair in cookie_pairs:
            pair = pair.strip()
            if '=' in pair:
                key, value = pair.split('=', 1)
                cookies[key.strip()] = value.strip()
    except Exception as e:
        print(f"Cookie解析失败: {e}")
        return {}
    
    return cookies

# 测试Cookie解析
test_cookie = (
    "x_clck=7wbgdh%7C2%7Cfy2%7C0%7C2038; "
    "_clsk=1p8olje%7C1753936853195%7C3%7C1%7Cs.clarity.ms%2Fcollect; "
    "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTI0MDYzLCJ1c2VybmFtZSI6InNtazk2IiwibmFtZSI6IlNtazk2IiwidHJ1c3RfbGV2ZWwiOjN9.BZlLyXnYWTpZ0uBtYbOSoMnpbBZLdmFnOeneGut0lmA"
)

print("🧪 测试Cookie解析功能...")
cookies = parse_cookie_string(test_cookie)
print(f"解析结果: {len(cookies)}个Cookie")
for key, value in cookies.items():
    print(f"  {key}: {value[:30]}...")

# 测试User-Agent池
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
]

print(f"\n🧪 测试User-Agent池...")
print(f"User-Agent数量: {len(user_agents)}")
random_ua = random.choice(user_agents)
print(f"随机选择: {random_ua[:50]}...")

print("\n✅ 核心功能测试通过！")