#!/usr/bin/env python3
"""
配置模块测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler.config import Config

def test_config():
    """测试配置模块功能"""
    print("🧪 测试配置模块...")
    
    # 创建配置实例
    config = Config()
    
    # 测试基本配置
    print(f"✅ 基础URL: {config.base_url}")
    print(f"✅ 目标URL: {config.target_url}")
    
    # 测试User-Agent池
    print(f"✅ User-Agent池大小: {len(config.user_agents)}")
    if len(config.user_agents) >= 10:
        print("✅ User-Agent池满足要求（>=10个）")
    else:
        print("❌ User-Agent池不足10个")
    
    # 测试随机User-Agent
    ua1 = config.get_random_user_agent()
    ua2 = config.get_random_user_agent()
    print(f"✅ 随机User-Agent 1: {ua1[:50]}...")
    print(f"✅ 随机User-Agent 2: {ua2[:50]}...")
    
    # 测试Cookie解析
    print(f"✅ Cookie数量: {len(config.cookies)}")
    if config.cookies:
        print("✅ Cookie解析成功")
        # 显示部分Cookie
        for i, (key, value) in enumerate(list(config.cookies.items())[:3]):
            print(f"   {key}: {value[:20]}...")
    else:
        print("❌ Cookie解析失败")
    
    # 测试请求头生成
    headers = config.get_headers()
    print(f"✅ 请求头数量: {len(headers)}")
    print(f"✅ User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
    
    # 测试延迟配置
    delay = config.get_random_delay()
    print(f"✅ 随机延迟: {delay:.2f}秒")
    
    # 测试配置验证
    is_valid = config.validate_config()
    if is_valid:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
    
    # 显示完整配置信息
    print("\n📋 完整配置信息:")
    print(config)
    
    return is_valid

if __name__ == "__main__":
    success = test_config()
    if success:
        print("\n🎉 配置模块测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 配置模块测试失败！")
        sys.exit(1)