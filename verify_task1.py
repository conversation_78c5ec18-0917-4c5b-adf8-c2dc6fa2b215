#!/usr/bin/env python3
"""
任务1验证脚本：验证项目基础结构和配置模块
"""

import os
import sys

def verify_directory_structure():
    """验证目录结构"""
    print("🔍 验证目录结构...")
    
    required_dirs = [
        "gemini_crawler",
        "results", 
        "examples"
    ]
    
    required_files = [
        "requirements.txt",
        "gemini_crawler/__init__.py",
        "gemini_crawler/config.py"
    ]
    
    base_path = os.path.dirname(os.path.abspath(__file__))
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = os.path.join(base_path, dir_name)
        if os.path.isdir(dir_path):
            print(f"✅ 目录存在: {dir_name}")
        else:
            print(f"❌ 目录缺失: {dir_name}")
            return False
    
    # 检查文件
    for file_name in required_files:
        file_path = os.path.join(base_path, file_name)
        if os.path.isfile(file_path):
            print(f"✅ 文件存在: {file_name}")
        else:
            print(f"❌ 文件缺失: {file_name}")
            return False
    
    return True

def verify_config_module():
    """验证配置模块功能"""
    print("\n🔍 验证配置模块...")
    
    try:
        # 导入配置模块
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from gemini_crawler.config import Config
        
        # 创建配置实例
        config = Config()
        
        # 验证User-Agent池
        if len(config.user_agents) >= 10:
            print(f"✅ User-Agent池满足要求: {len(config.user_agents)}个")
        else:
            print(f"❌ User-Agent池不足: {len(config.user_agents)}个")
            return False
        
        # 验证Cookie解析
        if config.cookies and len(config.cookies) > 0:
            print(f"✅ Cookie解析成功: {len(config.cookies)}个")
        else:
            print("❌ Cookie解析失败")
            return False
        
        # 验证基础配置
        if config.base_url and config.target_url:
            print(f"✅ URL配置正确: {config.target_url}")
        else:
            print("❌ URL配置错误")
            return False
        
        # 验证随机功能
        ua1 = config.get_random_user_agent()
        ua2 = config.get_random_user_agent()
        if ua1 and ua2:
            print("✅ 随机User-Agent功能正常")
        else:
            print("❌ 随机User-Agent功能异常")
            return False
        
        # 验证请求头生成
        headers = config.get_headers()
        if headers and 'User-Agent' in headers:
            print("✅ 请求头生成功能正常")
        else:
            print("❌ 请求头生成功能异常")
            return False
        
        # 验证配置验证功能
        if config.validate_config():
            print("✅ 配置验证功能正常")
        else:
            print("❌ 配置验证功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模块导入或测试失败: {e}")
        return False

def verify_requirements():
    """验证依赖文件"""
    print("\n🔍 验证依赖文件...")
    
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            content = f.read()
        
        required_packages = [
            "requests",
            "beautifulsoup4", 
            "lxml",
            "pandas",
            "click",
            "colorlog",
            "python-dotenv"
        ]
        
        for package in required_packages:
            if package in content:
                print(f"✅ 依赖包存在: {package}")
            else:
                print(f"❌ 依赖包缺失: {package}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖文件验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证任务1：创建项目基础结构和配置模块")
    print("=" * 60)
    
    # 验证目录结构
    if not verify_directory_structure():
        print("\n❌ 目录结构验证失败")
        return False
    
    # 验证配置模块
    if not verify_config_module():
        print("\n❌ 配置模块验证失败")
        return False
    
    # 验证依赖文件
    if not verify_requirements():
        print("\n❌ 依赖文件验证失败")
        return False
    
    print("\n🎉 任务1验证通过！")
    print("✅ 项目基础结构创建完成")
    print("✅ 配置模块功能正常")
    print("✅ 依赖文件配置正确")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)