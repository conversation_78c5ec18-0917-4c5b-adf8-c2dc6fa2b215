#!/usr/bin/env python3
"""
分析目标网站数据结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler.config import Config
from gemini_crawler.http_client import HTTPClient
from gemini_crawler.utils import setup_logging
from gemini_crawler.exceptions import WAFBlockedException, NetworkException


def analyze_website_structure():
    """分析网站数据结构"""
    print("🔍 分析目标网站数据结构...")
    
    # 设置日志
    logger = setup_logging('INFO')
    
    # 创建配置和HTTP客户端
    config = Config()
    
    try:
        with HTTPClient(config) as client:
            print(f"🌐 访问目标URL: {config.target_url}")
            
            # 尝试获取网站数据
            response = client.get()
            
            print(f"✅ 响应状态码: {response.status_code}")
            print(f"📄 响应内容类型: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"📏 响应内容长度: {len(response.text)} 字符")
            
            # 分析响应内容
            content = response.text
            
            # 检查是否为JSON格式
            if response.headers.get('Content-Type', '').startswith('application/json'):
                print("📊 检测到JSON格式数据")
                try:
                    import json
                    data = json.loads(content)
                    print(f"🔑 JSON数据结构: {type(data)}")
                    if isinstance(data, dict):
                        print(f"📋 JSON键: {list(data.keys())}")
                    elif isinstance(data, list) and len(data) > 0:
                        print(f"📋 数组长度: {len(data)}")
                        print(f"📋 第一个元素结构: {type(data[0])}")
                        if isinstance(data[0], dict):
                            print(f"📋 第一个元素键: {list(data[0].keys())}")
                except json.JSONDecodeError:
                    print("❌ JSON解析失败")
            
            # 检查是否为HTML格式
            elif 'html' in response.headers.get('Content-Type', '').lower():
                print("🌐 检测到HTML格式数据")
                
                # 查找可能的数据容器
                if '<script' in content:
                    print("📜 发现JavaScript代码")
                    # 查找可能的JSON数据
                    import re
                    json_patterns = [
                        r'var\s+\w+\s*=\s*(\{.*?\});',
                        r'const\s+\w+\s*=\s*(\{.*?\});',
                        r'let\s+\w+\s*=\s*(\{.*?\});',
                        r'window\.\w+\s*=\s*(\{.*?\});'
                    ]
                    
                    for pattern in json_patterns:
                        matches = re.findall(pattern, content, re.DOTALL)
                        if matches:
                            print(f"🔍 发现可能的JSON数据: {len(matches)} 个匹配")
                            break
                
                # 查找表格数据
                if '<table' in content:
                    print("📊 发现表格数据")
                
                # 查找列表数据
                if '<ul' in content or '<ol' in content:
                    print("📋 发现列表数据")
            
            # 保存响应内容用于进一步分析
            with open('website_response.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("💾 响应内容已保存到 website_response.html")
            
            # 显示前500个字符
            print("\n📝 响应内容预览:")
            print("-" * 50)
            print(content[:500])
            if len(content) > 500:
                print("...")
            print("-" * 50)
            
            return True
            
    except WAFBlockedException as e:
        print(f"🛡️ 被WAF拦截: {e}")
        print("💡 建议：可能需要调整反检测策略")
        return False
        
    except NetworkException as e:
        print(f"🌐 网络错误: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始分析目标网站数据结构")
    print("=" * 60)
    
    success = analyze_website_structure()
    
    if success:
        print("\n✅ 网站数据结构分析完成")
        print("💡 请查看 website_response.html 文件了解详细结构")
    else:
        print("\n❌ 网站数据结构分析失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)