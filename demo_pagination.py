#!/usr/bin/env python3
"""
演示分页爬取 - 爬取前5页
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawl_all_pages import PaginationCrawler
from gemini_crawler import setup_logging


def main():
    """演示分页爬取前5页"""
    print("🚀 演示分页爬取 - 前5页")
    print("=" * 40)
    
    # 设置日志
    setup_logging('INFO')
    
    # 创建分页爬虫
    crawler = PaginationCrawler()
    
    try:
        # 只爬取前5页进行演示
        result = crawler.crawl_all_pages(max_pages=5)
        
        # 打印摘要
        crawler.print_summary(result)
        
        # 保存结果
        if result['keys']:
            # 保存JSON格式
            json_file = crawler.storage.save_keys(
                result['keys'], 
                format='json',
                filename='demo_5pages_keys.json'
            )
            
            print(f"\n💾 演示结果已保存: {json_file}")
            print(f"🎉 演示完成！获取了 {len(result['keys'])} 个唯一的Gemini Key")
            
            # 显示前10个Key
            print(f"\n📋 前10个Key预览:")
            for i, key_data in enumerate(result['keys'][:10], 1):
                print(f"   {i:2d}. {key_data['key']} ({key_data['type']})")
        else:
            print("⚠️  未获取到任何Key")
    
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()