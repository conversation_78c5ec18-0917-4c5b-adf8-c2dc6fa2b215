#!/usr/bin/env python3
"""
数据解析器和验证器测试脚本
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gemini_crawler.parsers import DataParser
from gemini_crawler.validators import GeminiKeyValidator, DataQualityChecker
from gemini_crawler.utils import setup_logging


def test_key_validator():
    """测试Key验证器"""
    print("🧪 测试Key验证器...")
    
    validator = GeminiKeyValidator()
    
    # 测试有效的Key
    valid_keys = [
        "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",  # Google AI Studio格式
        "sk-1234567890abcdef1234567890abcdef1234567890abcdef",  # OpenAI格式
        "gsk_1234567890abcdef1234567890abcdef",  # Google Service格式
        "1234567890abcdef1234567890abcdef"  # 通用格式
    ]
    
    # 测试无效的Key
    invalid_keys = [
        "test123",  # 太短
        "demo_key_example",  # 包含无效关键词
        "AIza",  # 格式不完整
        "",  # 空字符串
        None,  # None值
        "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # 无效字符模式
    ]
    
    print("✅ 测试有效Key:")
    for key in valid_keys:
        is_valid = validator.is_valid_key(key)
        key_type = validator.get_key_type(key)
        print(f"   {key[:20]}... -> 有效: {is_valid}, 类型: {key_type}")
    
    print("✅ 测试无效Key:")
    for key in invalid_keys:
        is_valid = validator.is_valid_key(key)
        print(f"   {str(key)[:20]}... -> 有效: {is_valid}")
    
    # 测试Key清理功能
    print("✅ 测试Key清理:")
    dirty_keys = [
        "  AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK  ",
        "'sk-1234567890abcdef1234567890abcdef1234567890abcdef'",
        "Bearer AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
        "API_KEY=gsk_1234567890abcdef1234567890abcdef"
    ]
    
    for dirty_key in dirty_keys:
        cleaned = validator.clean_key(dirty_key)
        print(f"   原始: {dirty_key}")
        print(f"   清理: {cleaned}")
        print(f"   有效: {validator.is_valid_key(cleaned)}")
        print()
    
    return True


def test_json_parser():
    """测试JSON解析器"""
    print("🧪 测试JSON解析器...")
    
    parser = DataParser()
    
    # 模拟JSON响应数据
    test_json_data = {
        "keys": [
            {
                "key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK",
                "status": 200,
                "active": True
            },
            {
                "api_key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef",
                "state": "active",
                "valid": True
            },
            {
                "token": "gsk_1234567890abcdef1234567890abcdef",
                "status": 403,
                "active": False
            }
        ]
    }
    
    # 测试JSON解析
    json_text = json.dumps(test_json_data)
    results = parser.parse_json(json_text)
    
    print(f"✅ JSON解析结果: {len(results)} 个Key")
    for i, result in enumerate(results):
        print(f"   Key {i+1}: {result['key'][:20]}..., 状态: {result['status']}")
    
    # 测试状态200过滤
    status_200_keys = parser.extract_status_200_keys(results)
    print(f"✅ 状态200的Key: {len(status_200_keys)} 个")
    
    return True


def test_html_parser():
    """测试HTML解析器"""
    print("🧪 测试HTML解析器...")
    
    parser = DataParser()
    
    # 模拟HTML响应数据
    test_html = """
    <html>
    <head><title>Gemini Keys</title></head>
    <body>
        <table>
            <tr>
                <th>Key</th>
                <th>Status</th>
                <th>Active</th>
            </tr>
            <tr>
                <td>AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK</td>
                <td>200</td>
                <td>true</td>
            </tr>
            <tr>
                <td>sk-1234567890abcdef1234567890abcdef1234567890abcdef</td>
                <td>403</td>
                <td>false</td>
            </tr>
        </table>
        
        <script>
        var apiKeys = {
            "keys": [
                {"key": "gsk_1234567890abcdef1234567890abcdef", "status": 200}
            ]
        };
        </script>
        
        <ul>
            <li>AIzaSyBhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK</li>
            <li>sk-9876543210fedcba9876543210fedcba9876543210fedcba</li>
        </ul>
    </body>
    </html>
    """
    
    # 测试HTML解析
    results = parser.parse_html(test_html)
    
    print(f"✅ HTML解析结果: {len(results)} 个Key")
    for i, result in enumerate(results):
        print(f"   Key {i+1}: {result['key'][:20]}..., 状态: {result['status']}, 来源: {result['source']}")
    
    # 测试去重功能
    unique_results = parser.deduplicate_keys(results + results)  # 添加重复数据
    print(f"✅ 去重结果: 原始 {len(results + results)} 个 -> 去重后 {len(unique_results)} 个")
    
    return True


def test_text_extraction():
    """测试文本提取功能"""
    print("🧪 测试文本提取功能...")
    
    parser = DataParser()
    
    # 模拟包含Key的文本
    test_text = """
    这里有一些API Keys:
    AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK - 这是Google AI Studio Key
    sk-1234567890abcdef1234567890abcdef1234567890abcdef - OpenAI格式
    gsk_1234567890abcdef1234567890abcdef - Google Service Key
    
    还有一些无效的:
    test123 - 太短
    demo_key - 包含demo关键词
    """
    
    # 测试文本提取
    results = parser._extract_keys_from_text(test_text)
    
    print(f"✅ 文本提取结果: {len(results)} 个Key")
    for i, result in enumerate(results):
        print(f"   Key {i+1}: {result['key'][:20]}...")
    
    return True


def test_data_quality_checker():
    """测试数据质量检查器"""
    print("🧪 测试数据质量检查器...")
    
    checker = DataQualityChecker()
    
    # 模拟测试数据
    test_data = [
        {"key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK", "status": 200, "source": "api"},
        {"key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef", "status": 200},  # 缺少source
        {"key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK", "status": 403, "source": "web"},  # 重复key，不同状态
        {"status": 200, "source": "manual"},  # 缺少key
        {"key": "gsk_1234567890abcdef1234567890abcdef", "status": 200, "source": "scrape"}
    ]
    
    # 测试完整性检查
    completeness = checker.check_data_completeness(test_data)
    print("✅ 数据完整性检查:")
    print(f"   总记录数: {completeness['total_records']}")
    print(f"   完整记录: {completeness['complete_records']}")
    print(f"   不完整记录: {completeness['incomplete_records']}")
    print(f"   字段覆盖率: {completeness['field_coverage']}")
    
    # 测试一致性检查
    consistency = checker.check_data_consistency(test_data)
    print("✅ 数据一致性检查:")
    print(f"   重复Key: {consistency['duplicate_keys']}")
    print(f"   状态不一致: {consistency['inconsistent_status']}")
    print(f"   唯一Key: {consistency['unique_keys']}")
    
    return True


def test_mock_response_parsing():
    """测试模拟响应解析"""
    print("🧪 测试模拟响应解析...")
    
    parser = DataParser()
    
    # 模拟HTTP响应对象
    class MockResponse:
        def __init__(self, text, content_type):
            self.text = text
            self.headers = {'Content-Type': content_type}
    
    # 测试JSON响应
    json_response = MockResponse(
        json.dumps({
            "data": [
                {"key": "AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK", "status": 200},
                {"key": "sk-1234567890abcdef1234567890abcdef1234567890abcdef", "status": 403}
            ]
        }),
        'application/json'
    )
    
    json_results = parser.parse_response(json_response)
    print(f"✅ JSON响应解析: {len(json_results)} 个Key")
    
    # 测试HTML响应
    html_response = MockResponse(
        "<html><body>Key: AIzaSyDhZjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK</body></html>",
        'text/html'
    )
    
    html_results = parser.parse_response(html_response)
    print(f"✅ HTML响应解析: {len(html_results)} 个Key")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始数据解析和验证功能测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging('INFO')
    
    try:
        # 测试Key验证器
        if not test_key_validator():
            print("\n❌ Key验证器测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试JSON解析器
        if not test_json_parser():
            print("\n❌ JSON解析器测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试HTML解析器
        if not test_html_parser():
            print("\n❌ HTML解析器测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试文本提取
        if not test_text_extraction():
            print("\n❌ 文本提取测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试数据质量检查器
        if not test_data_quality_checker():
            print("\n❌ 数据质量检查器测试失败")
            return False
        
        print("\n" + "-" * 40)
        
        # 测试模拟响应解析
        if not test_mock_response_parsing():
            print("\n❌ 模拟响应解析测试失败")
            return False
        
        print("\n🎉 所有测试通过！")
        print("✅ Key验证器功能正常")
        print("✅ JSON解析器功能正常")
        print("✅ HTML解析器功能正常")
        print("✅ 文本提取功能正常")
        print("✅ 数据质量检查功能正常")
        print("✅ 响应解析功能正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)