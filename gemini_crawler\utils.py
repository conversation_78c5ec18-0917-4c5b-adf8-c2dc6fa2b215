"""
工具函数模块

提供爬虫相关的工具函数
"""

import time
import random
import hashlib
import json
from datetime import datetime
from typing import Any, Dict, List, Optional
import logging


def setup_logging(level: str = 'INFO', 
                 format_string: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        format_string: 日志格式字符串
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_string,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    return logging.getLogger('gemini_crawler')


def random_delay(min_seconds: float = 1.0, max_seconds: float = 5.0):
    """
    随机延迟
    
    Args:
        min_seconds: 最小延迟秒数
        max_seconds: 最大延迟秒数
    """
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def generate_request_id() -> str:
    """
    生成唯一的请求ID
    
    Returns:
        str: 请求ID
    """
    timestamp = str(time.time())
    random_str = str(random.randint(1000, 9999))
    return hashlib.md5((timestamp + random_str).encode()).hexdigest()[:8]


def safe_json_loads(text: str, default: Any = None) -> Any:
    """
    安全的JSON解析
    
    Args:
        text: JSON字符串
        default: 解析失败时的默认值
        
    Returns:
        Any: 解析结果或默认值
    """
    try:
        return json.loads(text)
    except (json.JSONDecodeError, TypeError):
        return default


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化的大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def get_timestamp(format_string: str = '%Y%m%d_%H%M%S') -> str:
    """
    获取格式化的时间戳
    
    Args:
        format_string: 时间格式字符串
        
    Returns:
        str: 格式化的时间戳
    """
    return datetime.now().strftime(format_string)


def validate_url(url: str) -> bool:
    """
    验证URL格式
    
    Args:
        url: 待验证的URL
        
    Returns:
        bool: URL是否有效
    """
    if not url or not isinstance(url, str):
        return False
    
    return url.startswith(('http://', 'https://'))


def clean_text(text: str) -> str:
    """
    清理文本内容
    
    Args:
        text: 原始文本
        
    Returns:
        str: 清理后的文本
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = ' '.join(text.split())
    
    # 移除特殊字符
    text = text.strip('\n\r\t ')
    
    return text


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 原始列表
        chunk_size: 块大小
        
    Returns:
        List[List[Any]]: 分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def retry_on_exception(max_retries: int = 3, 
                      delay: float = 1.0,
                      exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟
        exceptions: 需要重试的异常类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (attempt + 1))
                        continue
                    else:
                        raise last_exception
            
            if last_exception:
                raise last_exception
        
        return wrapper
    return decorator


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "Processing"):
        """
        初始化进度跟踪器
        
        Args:
            total: 总数量
            description: 描述信息
        """
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
    
    def update(self, increment: int = 1):
        """
        更新进度
        
        Args:
            increment: 增量
        """
        self.current += increment
        self._print_progress()
    
    def _print_progress(self):
        """打印进度信息"""
        if self.total == 0:
            return
        
        percentage = (self.current / self.total) * 100
        elapsed_time = time.time() - self.start_time
        
        if self.current > 0:
            eta = (elapsed_time / self.current) * (self.total - self.current)
            eta_str = f"ETA: {eta:.1f}s"
        else:
            eta_str = "ETA: --"
        
        print(f"\r{self.description}: {self.current}/{self.total} "
              f"({percentage:.1f}%) - {eta_str}", end="", flush=True)
        
        if self.current >= self.total:
            print()  # 换行
    
    def finish(self):
        """完成进度跟踪"""
        self.current = self.total
        self._print_progress()


class RateLimiter:
    """频率限制器"""
    
    def __init__(self, max_requests: int, time_window: float):
        """
        初始化频率限制器
        
        Args:
            max_requests: 时间窗口内最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def acquire(self) -> bool:
        """
        获取请求许可
        
        Returns:
            bool: 是否获得许可
        """
        current_time = time.time()
        
        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests 
                        if current_time - req_time < self.time_window]
        
        # 检查是否超过限制
        if len(self.requests) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests.append(current_time)
        return True
    
    def wait_if_needed(self):
        """如果需要，等待直到可以发送请求"""
        while not self.acquire():
            time.sleep(0.1)