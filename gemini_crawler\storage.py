"""
数据存储模块

提供多种格式的数据存储和输出功能
"""

import os
import json
import csv
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging

from .utils import get_timestamp, format_file_size
from .exceptions import StorageException


class DataStorage:
    """数据存储类"""
    
    def __init__(self, output_dir: str = "results"):
        """
        初始化数据存储
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.logger = logging.getLogger(__name__)
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的格式
        self.supported_formats = ['json', 'csv', 'txt', 'keys-only', 'xlsx']
        
        self.logger.info(f"数据存储初始化完成，输出目录: {self.output_dir}")
    
    def save_keys(self, 
                  keys: List[Dict[str, Any]], 
                  format: str = 'json',
                  filename: Optional[str] = None,
                  include_metadata: bool = True) -> str:
        """
        保存Key数据
        
        Args:
            keys: Key数据列表
            format: 输出格式 (json, csv, txt, xlsx)
            filename: 文件名，如果为None则自动生成
            include_metadata: 是否包含元数据
            
        Returns:
            str: 保存的文件路径
            
        Raises:
            StorageException: 存储失败
        """
        if format not in self.supported_formats:
            raise StorageException(f"不支持的格式: {format}")
        
        if not keys:
            self.logger.warning("没有数据需要保存")
            return ""
        
        try:
            # 生成文件名
            if filename is None:
                timestamp = get_timestamp()
                filename = f"gemini_keys_{timestamp}.{format}"
            
            filepath = self.output_dir / filename
            
            # 根据格式调用相应的保存方法
            if format == 'json':
                self._save_to_json(keys, filepath, include_metadata)
            elif format == 'csv':
                self._save_to_csv(keys, filepath, include_metadata)
            elif format == 'txt':
                self._save_to_txt(keys, filepath, include_metadata)
            elif format == 'keys-only':
                self._save_to_keys_only(keys, filepath)
            elif format == 'xlsx':
                self._save_to_xlsx(keys, filepath, include_metadata)
            
            # 获取文件信息
            file_size = os.path.getsize(filepath)
            self.logger.info(f"数据保存成功: {filepath} ({format_file_size(file_size)})")
            
            return str(filepath)
            
        except Exception as e:
            raise StorageException(f"数据保存失败: {e}")
    
    def _save_to_json(self, keys: List[Dict[str, Any]], filepath: Path, include_metadata: bool):
        """保存为JSON格式"""
        data = {
            'keys': keys
        }
        
        if include_metadata:
            data.update({
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'total_count': len(keys),
                    'format': 'json',
                    'version': '1.0',
                    'generated_by': 'Gemini Key Crawler'
                }
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _save_to_csv(self, keys: List[Dict[str, Any]], filepath: Path, include_metadata: bool):
        """保存为CSV格式"""
        if not keys:
            return
        
        # 获取所有字段
        all_fields = set()
        for key_data in keys:
            all_fields.update(key_data.keys())
        
        # 排序字段，确保key字段在前面
        priority_fields = ['key', 'status', 'type', 'source', 'validated']
        other_fields = sorted(all_fields - set(priority_fields))
        fieldnames = [f for f in priority_fields if f in all_fields] + other_fields
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            # 写入元数据（如果需要）
            if include_metadata:
                writer.writerow({fieldnames[0]: f"# Generated by Gemini Key Crawler"})
                writer.writerow({fieldnames[0]: f"# Timestamp: {datetime.now().isoformat()}"})
                writer.writerow({fieldnames[0]: f"# Total Count: {len(keys)}"})
                writer.writerow({})  # 空行
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据
            for key_data in keys:
                # 处理复杂字段
                row_data = {}
                for field in fieldnames:
                    value = key_data.get(field, '')
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value, ensure_ascii=False)
                    row_data[field] = value
                
                writer.writerow(row_data)
    
    def _save_to_txt(self, keys: List[Dict[str, Any]], filepath: Path, include_metadata: bool):
        """保存为TXT格式"""
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入元数据
            if include_metadata:
                f.write(f"# Gemini Key Crawler Results\n")
                f.write(f"# Generated: {datetime.now().isoformat()}\n")
                f.write(f"# Total Keys: {len(keys)}\n")
                f.write(f"# Format: Plain Text\n")
                f.write("\n")
            
            # 写入Key列表
            for i, key_data in enumerate(keys, 1):
                key = key_data.get('key', '')
                status = key_data.get('status', 'unknown')
                key_type = key_data.get('type', 'unknown')
                
                f.write(f"{i:4d}. {key}\n")
                if include_metadata:
                    f.write(f"      Status: {status}, Type: {key_type}\n")
                f.write("\n")

    def _save_to_keys_only(self, keys: List[Dict[str, Any]], filepath: Path):
        """保存为纯Key格式（每行一个Key）"""
        with open(filepath, 'w', encoding='utf-8') as f:
            for key_data in keys:
                key = key_data.get('key', '')
                if key:
                    f.write(key + '\n')
    
    def _save_to_xlsx(self, keys: List[Dict[str, Any]], filepath: Path, include_metadata: bool):
        """保存为Excel格式"""
        try:
            import pandas as pd
        except ImportError:
            raise StorageException("需要安装pandas库才能保存Excel格式: pip install pandas openpyxl")
        
        # 创建DataFrame
        df = pd.DataFrame(keys)
        
        # 创建Excel写入器
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 写入主数据
            df.to_excel(writer, sheet_name='Keys', index=False)
            
            # 写入元数据（如果需要）
            if include_metadata:
                metadata_df = pd.DataFrame([
                    ['Generated By', 'Gemini Key Crawler'],
                    ['Timestamp', datetime.now().isoformat()],
                    ['Total Count', len(keys)],
                    ['Format', 'Excel'],
                    ['Version', '1.0']
                ], columns=['Property', 'Value'])
                
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
    
    def save_report(self, 
                   report_data: Dict[str, Any], 
                   format: str = 'json',
                   filename: Optional[str] = None) -> str:
        """
        保存统计报告
        
        Args:
            report_data: 报告数据
            format: 输出格式
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if filename is None:
                timestamp = get_timestamp()
                filename = f"crawler_report_{timestamp}.{format}"
            
            filepath = self.output_dir / filename
            
            if format == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            elif format == 'txt':
                self._save_report_to_txt(report_data, filepath)
            
            else:
                raise StorageException(f"报告不支持格式: {format}")
            
            self.logger.info(f"报告保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            raise StorageException(f"报告保存失败: {e}")
    
    def _save_report_to_txt(self, report_data: Dict[str, Any], filepath: Path):
        """保存报告为TXT格式"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("Gemini Key Crawler - 统计报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基础信息
            if 'timestamp' in report_data:
                f.write(f"生成时间: {report_data['timestamp']}\n")
            
            if 'duration' in report_data:
                f.write(f"运行时长: {report_data['duration']:.1f}秒\n")
            
            f.write("\n")
            
            # 请求统计
            if 'requests' in report_data:
                req = report_data['requests']
                f.write("📡 请求统计:\n")
                f.write(f"   总请求数: {req.get('total', 0)}\n")
                f.write(f"   成功请求: {req.get('successful', 0)}\n")
                f.write(f"   失败请求: {req.get('failed', 0)}\n")
                f.write(f"   成功率: {req.get('success_rate', '0%')}\n\n")
            
            # Key统计
            if 'keys' in report_data:
                keys = report_data['keys']
                f.write("🔑 Key统计:\n")
                f.write(f"   发现总数: {keys.get('total_found', 0)}\n")
                f.write(f"   有效Key: {keys.get('valid', 0)}\n")
                f.write(f"   无效Key: {keys.get('invalid', 0)}\n")
                f.write(f"   状态200: {keys.get('status_200', 0)}\n")
                f.write(f"   重复Key: {keys.get('duplicates', 0)}\n")
                
                # Key类型分布
                if 'types' in keys and keys['types']:
                    f.write("   类型分布:\n")
                    for key_type, count in keys['types'].items():
                        f.write(f"     {key_type}: {count}\n")
                f.write("\n")
            
            # 错误统计
            if 'errors' in report_data:
                errors = report_data['errors']
                total_errors = sum(errors.values()) if isinstance(errors, dict) else 0
                if total_errors > 0:
                    f.write("❌ 错误统计:\n")
                    for error_type, count in errors.items():
                        if count > 0:
                            f.write(f"   {error_type}: {count}\n")
                    f.write("\n")
            
            # 性能统计
            if 'performance' in report_data:
                perf = report_data['performance']
                f.write("⚡ 性能统计:\n")
                for metric, value in perf.items():
                    f.write(f"   {metric}: {value}\n")
                f.write("\n")
            
            f.write("=" * 60 + "\n")
    
    def generate_summary_report(self, 
                              keys: List[Dict[str, Any]], 
                              stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成摘要报告
        
        Args:
            keys: Key数据列表
            stats: 统计数据
            
        Returns:
            Dict[str, Any]: 摘要报告
        """
        # 分析Key类型分布
        type_distribution = {}
        status_distribution = {}
        source_distribution = {}
        
        for key_data in keys:
            # 类型分布
            key_type = key_data.get('type', 'unknown')
            type_distribution[key_type] = type_distribution.get(key_type, 0) + 1
            
            # 状态分布
            status = key_data.get('status', 'unknown')
            status_distribution[status] = status_distribution.get(status, 0) + 1
            
            # 来源分布
            source = key_data.get('source', 'unknown')
            source_distribution[source] = source_distribution.get(source, 0) + 1
        
        # 生成报告
        report = {
            'summary': {
                'total_keys': len(keys),
                'unique_types': len(type_distribution),
                'unique_statuses': len(status_distribution),
                'unique_sources': len(source_distribution)
            },
            'distributions': {
                'types': type_distribution,
                'statuses': status_distribution,
                'sources': source_distribution
            },
            'statistics': stats,
            'timestamp': datetime.now().isoformat(),
            'generated_by': 'Gemini Key Crawler'
        }
        
        return report
    
    def backup_existing_file(self, filepath: Union[str, Path]) -> Optional[str]:
        """
        备份现有文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            Optional[str]: 备份文件路径，如果文件不存在则返回None
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            return None
        
        try:
            timestamp = get_timestamp()
            backup_name = f"{filepath.stem}_backup_{timestamp}{filepath.suffix}"
            backup_path = filepath.parent / backup_name
            
            # 复制文件
            import shutil
            shutil.copy2(filepath, backup_path)
            
            self.logger.info(f"文件备份成功: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"文件备份失败: {e}")
            return None
    
    def list_output_files(self, pattern: str = "*") -> List[Dict[str, Any]]:
        """
        列出输出目录中的文件
        
        Args:
            pattern: 文件名模式
            
        Returns:
            List[Dict[str, Any]]: 文件信息列表
        """
        files = []
        
        try:
            for filepath in self.output_dir.glob(pattern):
                if filepath.is_file():
                    stat = filepath.stat()
                    files.append({
                        'name': filepath.name,
                        'path': str(filepath),
                        'size': stat.st_size,
                        'size_formatted': format_file_size(stat.st_size),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'extension': filepath.suffix.lower()
                    })
            
            # 按修改时间排序
            files.sort(key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"列出文件失败: {e}")
        
        return files
    
    def clean_old_files(self, days: int = 7, pattern: str = "*") -> int:
        """
        清理旧文件
        
        Args:
            days: 保留天数
            pattern: 文件名模式
            
        Returns:
            int: 删除的文件数量
        """
        deleted_count = 0
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        
        try:
            for filepath in self.output_dir.glob(pattern):
                if filepath.is_file():
                    if filepath.stat().st_mtime < cutoff_time:
                        filepath.unlink()
                        deleted_count += 1
                        self.logger.info(f"删除旧文件: {filepath.name}")
            
            if deleted_count > 0:
                self.logger.info(f"清理完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            self.logger.error(f"清理文件失败: {e}")
        
        return deleted_count
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            Dict[str, Any]: 存储信息
        """
        try:
            files = self.list_output_files()
            total_size = sum(f['size'] for f in files)
            
            # 按格式分组
            format_stats = {}
            for file_info in files:
                ext = file_info['extension']
                if ext not in format_stats:
                    format_stats[ext] = {'count': 0, 'size': 0}
                format_stats[ext]['count'] += 1
                format_stats[ext]['size'] += file_info['size']
            
            return {
                'output_directory': str(self.output_dir),
                'total_files': len(files),
                'total_size': total_size,
                'total_size_formatted': format_file_size(total_size),
                'format_statistics': format_stats,
                'supported_formats': self.supported_formats,
                'latest_files': files[:5]  # 最新的5个文件
            }
            
        except Exception as e:
            self.logger.error(f"获取存储信息失败: {e}")
            return {
                'output_directory': str(self.output_dir),
                'error': str(e)
            }