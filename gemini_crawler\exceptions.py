"""
自定义异常模块

定义爬虫相关的异常类型
"""


class CrawlerException(Exception):
    """爬虫基础异常类"""
    pass


class WAFBlockedException(CrawlerException):
    """WAF拦截异常"""
    pass


class NetworkException(CrawlerException):
    """网络异常"""
    pass


class RateLimitException(CrawlerException):
    """频率限制异常"""
    pass


class AuthenticationException(CrawlerException):
    """认证异常"""
    pass


class ParseException(CrawlerException):
    """数据解析异常"""
    pass


class ValidationException(CrawlerException):
    """数据验证异常"""
    pass


class StorageException(CrawlerException):
    """存储异常"""
    pass